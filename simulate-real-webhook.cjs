/**
 * Simulate a real Dodo Payments webhook for the actual payment that was created
 * This simulates what Dodo Payments SHOULD send when the payment is completed
 */

const http = require('http');
const crypto = require('crypto');

// Use the actual payment data from your terminal logs
const WEBHOOK_URL = 'http://localhost:3001/api/webhooks/dodo-payments';
const PROFILE_ID = 'ed231924-c55e-4a68-abe7-282da0080f73';
const CUSTOMER_ID = 'cus_G2qglXwJNyHCeq2d9QVSx';
const PAYMENT_ID = 'pay_MLfN0eCyr1XSVQWCLlLdN';
const SUBSCRIPTION_ID = 'sub_TleexURQ0P8QEXrEVfsjr';

// Create realistic webhook events based on Dodo Payments documentation
const paymentSucceededEvent = {
  business_id: "biz_test_123",
  type: "payment.succeeded",
  timestamp: new Date().toISOString(),
  data: {
    payload_type: "Payment",
    payment_id: PAYMENT_ID,
    customer_id: CUSTOMER_ID,
    amount: 1000, // $10.00 in cents
    currency: "USD",
    status: "succeeded",
    created_at: new Date().toISOString(),
    metadata: {
      test: "true",
      user_id: PROFILE_ID,
      profile_id: PROFILE_ID,
      user_email: "<EMAIL>",
      payment_type: "api_direct",
      timestamp: new Date().toISOString()
    }
  }
};

const subscriptionActiveEvent = {
  business_id: "biz_test_123",
  type: "subscription.active",
  timestamp: new Date().toISOString(),
  data: {
    payload_type: "Subscription",
    subscription_id: SUBSCRIPTION_ID,
    customer_id: CUSTOMER_ID,
    status: "active",
    product_id: "pdt_iSJretaAUZA4a1ZeIBjMQ",
    recurring_pre_tax_amount: 1000,
    currency: "USD",
    current_period_start: Math.floor(Date.now() / 1000),
    current_period_end: Math.floor(Date.now() / 1000) + (30 * 24 * 60 * 60), // 30 days
    created_at: new Date().toISOString(),
    next_billing_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
    metadata: {
      test: "true",
      user_id: PROFILE_ID,
      profile_id: PROFILE_ID,
      user_email: "<EMAIL>",
      payment_type: "api_direct"
    }
  }
};

/**
 * Send webhook event to the server
 */
function sendWebhookEvent(event, eventName) {
  return new Promise((resolve) => {
    try {
      console.log(`\n🚀 Simulating real ${eventName} webhook...`);
      console.log('📍 Webhook URL:', WEBHOOK_URL);
      console.log('👤 Profile ID:', PROFILE_ID);
      console.log('💳 Customer ID:', CUSTOMER_ID);
      console.log('📦 Event data:', JSON.stringify(event, null, 2));
      
      const payload = JSON.stringify(event);
      const timestamp = Math.floor(Date.now() / 1000).toString();
      const webhookId = 'wh_' + crypto.randomBytes(16).toString('hex');
      
      // Create a fake signature (since we disabled verification)
      const signature = 'real_webhook_' + Date.now();
      
      console.log('\n🔐 Webhook headers:');
      console.log('  - webhook-id:', webhookId);
      console.log('  - webhook-timestamp:', timestamp);
      console.log('  - webhook-signature:', signature);
      
      const url = require('url');
      const parsedUrl = url.parse(WEBHOOK_URL);
      
      const options = {
        hostname: parsedUrl.hostname,
        port: parsedUrl.port,
        path: parsedUrl.path,
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Content-Length': Buffer.byteLength(payload),
          'webhook-id': webhookId,
          'webhook-timestamp': timestamp,
          'webhook-signature': signature
        }
      };
      
      const req = http.request(options, (res) => {
        let responseData = '';
        
        res.on('data', (chunk) => {
          responseData += chunk;
        });
        
        res.on('end', () => {
          console.log('\n📤 Webhook response:');
          console.log('  - Status:', res.statusCode, res.statusMessage);
          console.log('  - Response:', responseData);
          
          if (res.statusCode >= 200 && res.statusCode < 300) {
            console.log('✅ Webhook processed successfully!');
            resolve(true);
          } else {
            console.log('❌ Webhook processing failed!');
            resolve(false);
          }
        });
      });
      
      req.on('error', (error) => {
        console.error('💥 Error sending webhook:', error.message);
        resolve(false);
      });
      
      req.write(payload);
      req.end();
      
    } catch (error) {
      console.error('💥 Error sending webhook:', error.message);
      resolve(false);
    }
  });
}

/**
 * Main simulation function
 */
async function simulateRealWebhooks() {
  console.log('🎭 Simulating Real Dodo Payments Webhooks');
  console.log('=' .repeat(60));
  console.log('This simulates what Dodo Payments SHOULD send when your payment is completed');
  console.log('Using actual payment data from your terminal logs');
  console.log('=' .repeat(60));
  
  // Simulate payment.succeeded event
  const paymentSuccess = await sendWebhookEvent(paymentSucceededEvent, 'payment.succeeded');
  
  // Wait a bit between events
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  // Simulate subscription.active event
  const subscriptionSuccess = await sendWebhookEvent(subscriptionActiveEvent, 'subscription.active');
  
  console.log('\n' + '=' .repeat(60));
  console.log('📊 Simulation Results:');
  console.log(`  - Payment webhook: ${paymentSuccess ? '✅ PROCESSED' : '❌ FAILED'}`);
  console.log(`  - Subscription webhook: ${subscriptionSuccess ? '✅ PROCESSED' : '❌ FAILED'}`);
  
  if (paymentSuccess && subscriptionSuccess) {
    console.log('\n🎉 SUCCESS! Both webhooks processed correctly!');
    console.log('💰 Check your Supabase database - credits should be added and subscription should be active');
    console.log('👤 User should now have basic plan and extra credits');
  } else {
    console.log('\n⚠️  Some webhooks failed. Check the server logs for details.');
  }
  
  console.log('\n📝 Next Steps:');
  console.log('1. Check your user profile in the app to see if credits were added');
  console.log('2. Verify subscription status changed to "basic"');
  console.log('3. If this works, the issue is that Dodo Payments webhook URL needs to be updated');
}

// Run the simulation
simulateRealWebhooks().catch(console.error);
