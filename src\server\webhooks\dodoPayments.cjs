/**
 * Dodo Payments Webhook Handler (CommonJS)
 * Handles webhook events from Dodo Payments
 */
const crypto = require('crypto');
const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || process.env.SUPABASE_URL || '';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';

console.log('🔧 Supabase configuration:', {
  url: supabaseUrl ? 'configured' : 'missing',
  serviceKey: supabaseServiceKey ? 'configured' : 'missing',
  serviceKeyLength: supabaseServiceKey ? supabaseServiceKey.length : 0
});

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function handler(req, res) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    console.log('🔔 Webhook received:', {
      headers: req.headers,
      body: req.body,
      method: req.method
    });

    // Verify webhook signature
    const webhookSecret = process.env.DODO_PAYMENTS_WEBHOOK_SECRET || process.env.VITE_DODO_PAYMENTS_WEBHOOK_SECRET;
    if (!webhookSecret) {
      console.error('❌ Webhook secret not configured');
      return res.status(500).json({ error: 'Webhook secret not configured' });
    }

    const signature = req.headers['webhook-signature'];
    const id = req.headers['webhook-id'];
    const timestamp = req.headers['webhook-timestamp'];
    
    console.log('🔐 Webhook headers:', { signature, id, timestamp });
    
    if (!signature || !id || !timestamp) {
      console.error('❌ Missing webhook signature headers');
      return res.status(400).json({ error: 'Missing webhook signature headers' });
    }

    // Create string to sign
    const payload = req.body;
    const stringToSign = `${id}.${timestamp}.${JSON.stringify(payload)}`;
    
    console.log('📝 String to sign:', stringToSign);
    
    // Check if this is a test webhook from the debug page
    const isTestWebhook = signature.startsWith('test_signature_');

    if (!isTestWebhook) {
      // Compute HMAC with SHA256 for real webhooks
      const computedSignature = crypto
        .createHmac('sha256', webhookSecret)
        .update(stringToSign)
        .digest('hex');

      console.log('🔑 Computed signature:', computedSignature);
      console.log('🔑 Received signature:', signature);

      // Compare signatures using a constant-time comparison function
      const isValid = crypto.timingSafeEqual(
        Buffer.from(signature),
        Buffer.from(computedSignature)
      );

      if (!isValid) {
        console.error('❌ Invalid webhook signature');
        return res.status(401).json({ error: 'Invalid webhook signature' });
      }
    } else {
      console.log('🧪 Test webhook detected, skipping signature verification');
    }

    console.log('✅ Webhook signature verified');

    // Process the webhook event
    const { type, data } = payload;
    console.log(`🎯 Processing webhook event: ${type}`, data);

    switch (type) {
      case 'subscription.active':
        await handleSubscriptionActive(data);
        break;
        
      case 'subscription.updated':
        await handleSubscriptionUpdated(data);
        break;
        
      case 'subscription.on_hold':
        await handleSubscriptionOnHold(data);
        break;
        
      case 'subscription.failed':
        await handleSubscriptionFailed(data);
        break;
        
      case 'subscription.renewed':
        await handleSubscriptionRenewed(data);
        break;
        
      case 'subscription.plan_changed':
        await handleSubscriptionPlanChanged(data);
        break;
        
      case 'payment.succeeded':
        await handlePaymentSucceeded(data);
        break;
        
      case 'payment.failed':
        await handlePaymentFailed(data);
        break;
        
      default:
        console.log(`⚠️ Unhandled webhook event type: ${type}`);
    }

    console.log('✅ Webhook processed successfully');
    // Return a 200 success response
    return res.status(200).json({ received: true, type, processed: true });
  } catch (error) {
    console.error('💥 Error processing webhook:', {
      message: error.message,
      stack: error.stack,
      error: error
    });
    return res.status(500).json({ 
      error: 'Error processing webhook',
      details: error.message,
      stack: error.stack
    });
  }
}

// Webhook event handler functions
async function handleSubscriptionActive(data) {
  const { subscription_id, customer_id, status, current_period_end, metadata } = data;

  try {
    console.log('🔄 Handling subscription.active:', { subscription_id, customer_id, status, metadata });

    // Try to update by customer_id first
    let { data: updateResult, error } = await supabase
      .from('profiles')
      .update({
        payment_status: 'active',
        subscription_id: subscription_id,
        subscription_end_date: new Date(current_period_end * 1000).toISOString(),
        subscription_plan: 'basic'
      })
      .eq('customer_id', customer_id)
      .select();

    // If no profile found by customer_id, try using metadata.profile_id
    if (!updateResult || updateResult.length === 0) {
      if (metadata?.profile_id) {
        console.log('🔄 Customer ID not found, trying with metadata profile_id:', metadata.profile_id);
        const { data: fallbackResult, error: fallbackError } = await supabase
          .from('profiles')
          .update({
            payment_status: 'active',
            subscription_id: subscription_id,
            subscription_end_date: new Date(current_period_end * 1000).toISOString(),
            subscription_plan: 'basic'
          })
          .eq('id', metadata.profile_id)
          .select();

        if (fallbackError) {
          console.error('❌ Error updating profile with fallback:', fallbackError);
          throw fallbackError;
        }

        updateResult = fallbackResult;
      } else {
        console.error('❌ No profile found for customer_id and no profile_id in metadata');
        throw new Error('Profile not found');
      }
    }

    if (error) {
      console.error('❌ Error updating profile:', error);
      throw error;
    }

    console.log('✅ Profile updated with active subscription:', updateResult);
    console.log(`✅ Subscription ${subscription_id} activated for customer ${customer_id}`);
  } catch (error) {
    console.error('💥 Error processing subscription event:', error);
    throw error;
  }
}

async function handleSubscriptionUpdated(data) {
  console.log('🔄 Handling subscription.updated:', data);
  // For now, treat updated the same as active
  await handleSubscriptionActive(data);
}

async function handleSubscriptionOnHold(data) {
  const { subscription_id, customer_id, status } = data;
  
  try {
    console.log('🔄 Handling subscription.on_hold:', { subscription_id, customer_id, status });
    
    // Update user profile with on hold subscription
    const { data: updateResult, error } = await supabase
      .from('profiles')
      .update({
        subscription_status: 'on_hold',
        updated_at: new Date().toISOString()
      })
      .eq('dodo_customer_id', customer_id)
      .select();
    
    if (error) {
      console.error('❌ Error updating profile:', error);
      throw error;
    }
    
    console.log('✅ Profile updated with on hold subscription:', updateResult);
    console.log(`✅ Subscription ${subscription_id} on hold for customer ${customer_id}`);
  } catch (error) {
    console.error('💥 Error processing subscription on hold event:', error);
    throw error;
  }
}

async function handleSubscriptionFailed(data) {
  const { subscription_id, customer_id, status } = data;
  
  try {
    console.log('🔄 Handling subscription.failed:', { subscription_id, customer_id, status });
    
    // Update user profile with failed subscription
    const { data: updateResult, error } = await supabase
      .from('profiles')
      .update({
        subscription_status: 'failed',
        updated_at: new Date().toISOString()
      })
      .eq('dodo_customer_id', customer_id)
      .select();
    
    if (error) {
      console.error('❌ Error updating profile:', error);
      throw error;
    }
    
    console.log('✅ Profile updated with failed subscription:', updateResult);
    console.log(`❌ Subscription ${subscription_id} failed for customer ${customer_id}`);
  } catch (error) {
    console.error('💥 Error processing subscription failed event:', error);
    throw error;
  }
}

async function handleSubscriptionRenewed(data) {
  console.log('🔄 Handling subscription.renewed:', data);
  // For now, treat renewed the same as active
  await handleSubscriptionActive(data);
}

async function handleSubscriptionPlanChanged(data) {
  console.log('🔄 Handling subscription.plan_changed:', data);
  // For now, treat plan changed the same as active
  await handleSubscriptionActive(data);
}

async function handlePaymentSucceeded(data) {
  const { payment_id, customer_id, amount, currency, metadata } = data;

  try {
    console.log('🔄 Handling payment.succeeded:', { payment_id, customer_id, amount, currency, metadata });

    // Log payment to payments table
    const { data: paymentResult, error: paymentError } = await supabase
      .from('payments')
      .insert({
        payment_id: payment_id,
        customer_id: customer_id,
        amount: amount,
        currency: currency,
        status: 'succeeded',
        metadata: metadata,
        created_at: new Date().toISOString()
      })
      .select();

    if (paymentError) {
      console.error('❌ Error logging payment:', paymentError);
      throw paymentError;
    }

    console.log('✅ Payment logged to database:', paymentResult);

    // Find the profile by customer_id and add credits
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('id')
      .eq('customer_id', customer_id)
      .single();

    if (profileError || !profile) {
      console.error('❌ Profile not found for customer_id:', customer_id, profileError);
      // Try to find by metadata profile_id if available
      if (metadata?.profile_id) {
        console.log('🔄 Trying to add credits using metadata profile_id:', metadata.profile_id);
        await addCreditsToProfile(metadata.profile_id, amount, metadata);
      }
    } else {
      console.log('✅ Found profile:', profile.id);
      await addCreditsToProfile(profile.id, amount, metadata);
    }

    console.log(`✅ Payment ${payment_id} succeeded for customer ${customer_id}`);
  } catch (error) {
    console.error('💥 Error processing payment succeeded event:', error);
    throw error;
  }
}

async function addCreditsToProfile(profileId, amount, metadata) {
  try {
    // Calculate credits: $1 = 10 credits (or use metadata.credits if provided)
    const creditsToAdd = metadata?.credits || Math.floor(amount / 100 * 10);

    console.log(`💰 Adding ${creditsToAdd} credits to profile ${profileId}`);

    // Add credits using the add_credits RPC function
    const { data: creditResult, error: creditError } = await supabase.rpc('add_credits', {
      user_id: profileId,
      amount: creditsToAdd,
      credit_type: 'purchase',
      description: `Payment successful - ${amount/100} ${metadata?.currency || 'USD'} purchase`
    });

    if (creditError) {
      console.error('❌ Error adding credits:', creditError);
    } else {
      console.log('✅ Credits added successfully:', creditResult);
    }
  } catch (error) {
    console.error('💥 Error adding credits to profile:', error);
  }
}

async function handlePaymentFailed(data) {
  const { payment_id, customer_id, amount, currency, error: paymentError } = data;
  
  try {
    console.log('🔄 Handling payment.failed:', { payment_id, customer_id, amount, currency, paymentError });
    
    // Log failed payment to payments table
    const { data: paymentResult, error: dbError } = await supabase
      .from('payments')
      .insert({
        payment_id: payment_id,
        customer_id: customer_id,
        amount: amount,
        currency: currency,
        status: 'failed',
        error_message: paymentError,
        created_at: new Date().toISOString()
      })
      .select();
    
    if (dbError) {
      console.error('❌ Error logging failed payment:', dbError);
      throw dbError;
    }
    
    console.log('✅ Failed payment logged to database:', paymentResult);
    console.log(`❌ Payment ${payment_id} failed for customer ${customer_id}`);
  } catch (error) {
    console.error('💥 Error processing payment failed event:', error);
    throw error;
  }
}

module.exports = handler;