/**
 * Test Supabase connection and RPC functions
 */
const { createClient } = require('@supabase/supabase-js');
const dotenv = require('dotenv');
const path = require('path');

// Load environment variables
dotenv.config({ path: path.join(__dirname, '.env') });

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || process.env.SUPABASE_URL || '';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';

console.log('🔧 Supabase configuration:');
console.log('  - URL:', supabaseUrl ? 'configured' : 'missing');
console.log('  - Service Key:', supabaseServiceKey ? 'configured' : 'missing');
console.log('  - Service Key Length:', supabaseServiceKey ? supabaseServiceKey.length : 0);

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase configuration');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

const PROFILE_ID = 'ba40622d-eaae-4437-838a-28d3910360b4';

async function testSupabaseConnection() {
  try {
    console.log('\n🧪 Testing Supabase connection...');
    
    // Test 1: Basic connection test
    console.log('\n1️⃣ Testing basic connection...');
    const { data: healthCheck, error: healthError } = await supabase
      .from('profiles')
      .select('id')
      .limit(1);
    
    if (healthError) {
      console.error('❌ Basic connection failed:', healthError);
      return false;
    }
    console.log('✅ Basic connection successful');
    
    // Test 2: Check if profile exists
    console.log('\n2️⃣ Checking if profile exists...');
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('id, credits, permanent_credits, email')
      .eq('id', PROFILE_ID)
      .single();
    
    if (profileError) {
      console.error('❌ Profile lookup failed:', profileError);
      return false;
    }
    
    if (!profile) {
      console.error('❌ Profile not found');
      return false;
    }
    
    console.log('✅ Profile found:', {
      id: profile.id,
      email: profile.email,
      credits: profile.credits,
      permanent_credits: profile.permanent_credits
    });
    
    // Test 3: Test add_credits RPC function
    console.log('\n3️⃣ Testing add_credits RPC function...');
    const { data: creditResult, error: creditError } = await supabase.rpc('add_credits', {
      user_id: PROFILE_ID,
      amount: 10,
      credit_type: 'purchase',
      description: 'Test credit addition from webhook test'
    });
    
    if (creditError) {
      console.error('❌ add_credits RPC failed:', creditError);
      return false;
    }
    
    console.log('✅ add_credits RPC successful:', creditResult);
    
    // Test 4: Verify credits were added
    console.log('\n4️⃣ Verifying credits were added...');
    const { data: updatedProfile, error: verifyError } = await supabase
      .from('profiles')
      .select('credits, permanent_credits')
      .eq('id', PROFILE_ID)
      .single();
    
    if (verifyError) {
      console.error('❌ Credit verification failed:', verifyError);
      return false;
    }
    
    console.log('✅ Updated profile credits:', {
      credits: updatedProfile.credits,
      permanent_credits: updatedProfile.permanent_credits
    });
    
    // Test 5: Test payments table insert
    console.log('\n5️⃣ Testing payments table insert...');
    const { data: paymentResult, error: paymentError } = await supabase
      .from('payments')
      .insert({
        user_id: PROFILE_ID,
        customer_id: 'test_customer_123',
        amount: 1000,
        status: 'succeeded',
        metadata: { test: true }
      })
      .select();
    
    if (paymentError) {
      console.error('❌ Payment insert failed:', paymentError);
      return false;
    }
    
    console.log('✅ Payment insert successful:', paymentResult);
    
    return true;
    
  } catch (error) {
    console.error('💥 Unexpected error:', error);
    return false;
  }
}

// Run the test
testSupabaseConnection()
  .then(success => {
    if (success) {
      console.log('\n🎉 All Supabase tests passed!');
      console.log('The webhook should work correctly now.');
    } else {
      console.log('\n❌ Some Supabase tests failed.');
      console.log('Check the errors above to fix the issues.');
    }
  })
  .catch(error => {
    console.error('💥 Test execution failed:', error);
  });
