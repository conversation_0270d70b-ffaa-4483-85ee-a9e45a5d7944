// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://alywdwwqrtddplqsbksd.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFseXdkd3dxcnRkZHBscXNia3NkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQwMjQyNTIsImV4cCI6MjA0OTYwMDI1Mn0.kiuDTgrGVi4rbZ3XYSIfqTTsiNUCvByDo5aDuXkwsZQ";

console.log('Supabase client configuration:', {
  url: SUPABASE_URL,
  keyPrefix: SUPABASE_PUBLISHABLE_KEY.substring(0, 20) + '...'
});

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

const isDevelopment = import.meta.env.DEV;
const SITE_URL = isDevelopment 
  ? 'http://localhost:5174'  // Local development
  : 'https://pixelkeywording.com'; // Production

console.log('Current environment:', isDevelopment ? 'Development' : 'Production');
console.log('Site URL:', SITE_URL);

// Clear only old auth sessions on startup, but preserve current auth flow
if (typeof window !== 'undefined') {
  console.log('Clearing old auth sessions to fix authentication issues');

  // Only clear sessions from the old project, not the current one
  Object.keys(localStorage).forEach(key => {
    if (key.includes('aibdxsebwhalbnugsqel') ||
        key.includes('pixelkeywording-auth-v2') ||
        key.includes('pixelkeywording-auth-v1')) {
      console.log('Removing old auth key:', key);
      localStorage.removeItem(key);
    }
  });

  // Clear old session storage
  Object.keys(sessionStorage).forEach(key => {
    if (key.includes('aibdxsebwhalbnugsqel') ||
        key.includes('pixelkeywording-auth-v2') ||
        key.includes('pixelkeywording-auth-v1')) {
      console.log('Removing old session key:', key);
      sessionStorage.removeItem(key);
    }
  });
}

// Function to get the current origin, handling both development and production
const getCurrentOrigin = () => {
  if (typeof window === 'undefined') return SITE_URL;
  // Always return the main domain in production to avoid subdomain issues
  if (!isDevelopment) return SITE_URL;
  return window.location.origin;
};

// Create Supabase client with auth logging disabled
export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    persistSession: true,
    autoRefreshToken: true,
    detectSessionInUrl: true,
    flowType: 'pkce',
    storageKey: 'pixelkeywording-auth-v3',
    storage: {
      getItem: (key) => {
        try {
          if (typeof window === 'undefined') return null;
          const storedSession = localStorage.getItem(key);
          console.log(`Getting auth item ${key}:`, storedSession ? 'found' : 'not found');
          return storedSession;
        } catch (error) {
          console.error('Error getting auth session from storage:', error);
          return null;
        }
      },
      setItem: (key, value) => {
        try {
          if (typeof window === 'undefined') return;
          localStorage.setItem(key, value);
          console.log(`Setting auth item ${key}:`, 'success');
        } catch (error) {
          console.error('Error setting auth session to storage:', error);
        }
      },
      removeItem: (key) => {
        try {
          if (typeof window === 'undefined') return;
          localStorage.removeItem(key);
          console.log(`Removing auth item ${key}:`, 'success');
        } catch (error) {
          console.error('Error removing auth session from storage:', error);
        }
      },
    },
    debug: false // Disable auth debug logging
  }
});