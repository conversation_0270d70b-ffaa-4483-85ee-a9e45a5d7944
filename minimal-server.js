// Minimal server test to isolate issues
import express from 'express';

const app = express();
const PORT = 3003;

// Simple middleware
app.use(express.json());

// Basic route
app.get('/', (req, res) => {
  console.log('Root route hit');
  res.json({ message: 'Minimal server working', timestamp: new Date().toISOString() });
});

// Health route
app.get('/health', (req, res) => {
  console.log('Health route hit');
  res.json({ status: 'ok' });
});

// Start server
app.listen(PORT, () => {
  console.log(`Minimal server running on port ${PORT}`);
  console.log(`Test with: http://localhost:${PORT}`);
});

// Handle process termination
process.on('SIGINT', () => {
  console.log('\nServer shutting down...');
  process.exit(0);
});