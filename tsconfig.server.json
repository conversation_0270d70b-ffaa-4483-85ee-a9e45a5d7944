{"compilerOptions": {"module": "ESNext", "moduleResolution": "node", "outDir": "./dist", "target": "ES2020", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": false, "declaration": false, "removeComments": true, "skipLibCheck": true, "allowJs": true, "allowImportingTsExtensions": false, "noEmit": false, "baseUrl": ".", "paths": {"@/*": ["./src/*"]}}, "include": ["src/server/**/*"], "exclude": ["node_modules", "dist", "src/client"]}