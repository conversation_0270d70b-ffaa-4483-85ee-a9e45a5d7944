/**
 * Dodo Payments service integration for Node.js/TypeScript
 */
import axios, { AxiosInstance } from 'axios';
import crypto from 'crypto';
import { createClient } from '@supabase/supabase-js';
import {
  CreatePaymentRequest,
  PaymentResponse,
  CreateSubscriptionRequest,
  SubscriptionResponse,
  PaymentRecord,
  SubscriptionRecord,
  PaymentStatus,
  SubscriptionStatus,
  WebhookEvent
} from '../types/payment';

export class DodoPaymentsService {
  private client: AxiosInstance;
  private apiKey: string;
  private webhookSecret: string;
  private mode: string;
  private supabase: any;

  constructor() {
    this.apiKey = process.env.DODO_PAYMENTS_API_KEY || '';
    this.webhookSecret = process.env.DODO_PAYMENTS_WEBHOOK_SECRET || '';
    this.mode = process.env.DODO_PAYMENTS_MODE || 'test';
    const apiUrl = process.env.DODO_PAYMENTS_API_URL || 'https://api.dodopayments.com';

    if (!this.apiKey) {
      throw new Error('DODO_PAYMENTS_API_KEY environment variable is required');
    }

    // Initialize Dodo Payments client
    this.client = axios.create({
      baseURL: apiUrl,
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
        'User-Agent': 'DodoPayments-NodeJS/1.0.0'
      },
      timeout: 30000
    });

    // Initialize Supabase client
    const supabaseUrl = process.env.SUPABASE_URL;
    const supabaseKey = process.env.SUPABASE_ANON_KEY;
    
    if (supabaseUrl && supabaseKey) {
      this.supabase = createClient(supabaseUrl, supabaseKey);
    }

    console.log(`Initializing Dodo Payments client in ${this.mode} mode`);
  }

  async createPayment(
    paymentRequest: CreatePaymentRequest,
    userId?: string
  ): Promise<PaymentResponse> {
    try {
      // Customer is required - use provided customer or default
      const customerData = paymentRequest.customer ? {
        ...(paymentRequest.customer.customer_id && { customer_id: paymentRequest.customer.customer_id }),
        ...(paymentRequest.customer.email && { email: paymentRequest.customer.email }),
        ...(paymentRequest.customer.name && { name: paymentRequest.customer.name })
      } : {
        customer_id: userId ? `cust_${userId}` : 'default_customer'
      };

      // Billing is required - use provided billing or default
      const billingData = paymentRequest.billing || {
        street: '123 Default Street',
        city: 'Default City',
        state: 'Default State',
        country: 'US',
        zipcode: '12345'
      };

      // Format request for Dodo Payments API
      const paymentData = {
        billing: billingData,
        customer: customerData,
        product_cart: paymentRequest.product_cart.map(item => ({
          product_id: item.product_id,
          quantity: item.quantity || 1,
          amount: item.amount
        })),
        ...(paymentRequest.return_url && { return_url: paymentRequest.return_url }),
        ...(paymentRequest.billing_currency && { billing_currency: paymentRequest.billing_currency }),
        ...(paymentRequest.allowed_payment_method_types && { 
          allowed_payment_method_types: paymentRequest.allowed_payment_method_types 
        })
      };

      console.log(`Creating payment with Dodo Payments API in ${this.mode} mode`);
      console.log('Payment data:', JSON.stringify(paymentData, null, 2));

      // Create payment with Dodo Payments
      const response = await this.client.post('/payments', paymentData);
      const paymentResponse = response.data;

      console.log(`Successfully created payment with Dodo Payments API: ${paymentResponse.id}`);

      // Save payment record to database
      if (this.supabase) {
        const paymentRecord: Omit<PaymentRecord, '_id'> = {
          payment_id: paymentResponse.id,
          user_id: userId,
          customer_id: paymentRequest.customer?.customer_id,
          amount: paymentRequest.product_cart.reduce((sum, item) => sum + item.amount, 0),
          currency: paymentRequest.billing_currency || 'USD',
          status: PaymentStatus.PENDING,
          product_id: paymentRequest.product_cart[0]?.product_id,
          metadata: paymentRequest.metadata,
          created_at: new Date(),
          updated_at: new Date()
        };

        await this.supabase
          .from('payments')
          .insert([paymentRecord]);
      }

      return {
        id: paymentResponse.id,
        url: paymentResponse.url,
        checkout_url: paymentResponse.checkout_url || paymentResponse.url,
        status: paymentResponse.status,
        expires_at: paymentResponse.expires_at
      };

    } catch (error: any) {
      console.error('Error creating payment with Dodo Payments API:', error.message);
      console.error('Error type:', error.constructor.name);
      console.error('API Key configured:', !!this.apiKey);
      console.error('API Mode:', this.mode);
      
      if (error.response) {
        console.error('API Response Status:', error.response.status);
        console.error('API Response Data:', error.response.data);
      }
      
      throw error;
    }
  }

  async createSubscription(
    subscriptionRequest: CreateSubscriptionRequest,
    userId?: string
  ): Promise<SubscriptionResponse> {
    try {
      // Format request for Dodo Payments API
      const subscriptionData = {
        customer: {
          customer_id: subscriptionRequest.customer.customer_id,
          email: subscriptionRequest.customer.email,
          name: subscriptionRequest.customer.name
        },
        product_id: subscriptionRequest.product_id,
        billing: subscriptionRequest.billing,
        quantity: subscriptionRequest.quantity || 1,
        payment_link: subscriptionRequest.payment_link !== false,
        metadata: subscriptionRequest.metadata || {},
        ...(subscriptionRequest.subscription_id && { subscription_id: subscriptionRequest.subscription_id })
      };

      console.log(`Creating subscription with Dodo Payments API in ${this.mode} mode`);
      console.log('Subscription data:', JSON.stringify(subscriptionData, null, 2));

      // Create subscription with Dodo Payments
      const response = await this.client.post('/subscriptions', subscriptionData);
      const subscriptionResponse = response.data;

      console.log(`Successfully created subscription with Dodo Payments API: ${subscriptionResponse.subscription_id}`);

      // Save subscription record to database
      if (this.supabase) {
        const subscriptionRecord: Omit<SubscriptionRecord, '_id'> = {
          subscription_id: subscriptionResponse.subscription_id,
          user_id: userId,
          customer_id: subscriptionRequest.customer.customer_id!,
          product_id: subscriptionRequest.product_id,
          status: SubscriptionStatus.ACTIVE,
          metadata: subscriptionRequest.metadata,
          created_at: new Date(),
          updated_at: new Date()
        };

        await this.supabase
          .from('subscriptions')
          .insert([subscriptionRecord]);
      }

      return {
        subscription_id: subscriptionResponse.subscription_id,
        customer_id: subscriptionRequest.customer.customer_id!,
        status: subscriptionResponse.status,
        product_id: subscriptionRequest.product_id,
        payment_url: subscriptionResponse.payment_url
      };

    } catch (error: any) {
      console.error('Error creating subscription with Dodo Payments API:', error.message);
      console.error('Error type:', error.constructor.name);
      console.error('API Key configured:', !!this.apiKey);
      console.error('API Mode:', this.mode);
      
      if (error.response) {
        console.error('API Response Status:', error.response.status);
        console.error('API Response Data:', error.response.data);
      }
      
      throw error;
    }
  }

  async getPayment(paymentId: string): Promise<PaymentRecord | null> {
    if (!this.supabase) return null;

    try {
      const { data, error } = await this.supabase
        .from('payments')
        .select('*')
        .eq('payment_id', paymentId)
        .single();

      if (error) {
        console.error('Error fetching payment:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Error fetching payment:', error);
      return null;
    }
  }

  async updatePaymentStatus(
    paymentId: string,
    status: PaymentStatus,
    metadata?: Record<string, any>
  ): Promise<boolean> {
    if (!this.supabase) return false;

    try {
      const updateData: any = {
        status,
        updated_at: new Date()
      };

      if (metadata) {
        updateData.metadata = metadata;
      }

      const { error } = await this.supabase
        .from('payments')
        .update(updateData)
        .eq('payment_id', paymentId);

      if (error) {
        console.error('Error updating payment status:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error updating payment status:', error);
      return false;
    }
  }

  async updateSubscriptionStatus(
    subscriptionId: string,
    status: SubscriptionStatus,
    metadata?: Record<string, any>
  ): Promise<boolean> {
    if (!this.supabase) return false;

    try {
      const updateData: any = {
        status,
        updated_at: new Date()
      };

      if (metadata) {
        updateData.metadata = metadata;
      }

      const { error } = await this.supabase
        .from('subscriptions')
        .update(updateData)
        .eq('subscription_id', subscriptionId);

      if (error) {
        console.error('Error updating subscription status:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error updating subscription status:', error);
      return false;
    }
  }

  verifyWebhookSignature(
    body: string,
    signature: string,
    webhookId: string,
    timestamp: string
  ): boolean {
    try {
      // Create string to sign
      const stringToSign = `${webhookId}.${timestamp}.${body}`;
      
      // Compute HMAC with SHA256
      const computedSignature = crypto
        .createHmac('sha256', this.webhookSecret)
        .update(stringToSign)
        .digest('hex');
      
      // Compare signatures using constant-time comparison
      return crypto.timingSafeEqual(
        Buffer.from(signature),
        Buffer.from(computedSignature)
      );
    } catch (error) {
      console.error('Error verifying webhook signature:', error);
      return false;
    }
  }

  async processWebhookEvent(event: WebhookEvent): Promise<void> {
    try {
      console.log(`Processing webhook event: ${event.type}`);

      switch (event.type) {
        case 'payment.succeeded':
          await this.handlePaymentSucceeded(event.data);
          break;
        case 'payment.failed':
          await this.handlePaymentFailed(event.data);
          break;
        case 'subscription.active':
          await this.handleSubscriptionActive(event.data);
          break;
        case 'subscription.on_hold':
          await this.handleSubscriptionOnHold(event.data);
          break;
        case 'subscription.failed':
          await this.handleSubscriptionFailed(event.data);
          break;
        case 'subscription.renewed':
          await this.handleSubscriptionRenewed(event.data);
          break;
        case 'subscription.plan_changed':
          await this.handleSubscriptionPlanChanged(event.data);
          break;
        default:
          console.warn(`Unhandled webhook event type: ${event.type}`);
      }
    } catch (error) {
      console.error(`Error processing webhook event ${event.type}:`, error);
      throw error;
    }
  }

  private async handlePaymentSucceeded(data: Record<string, any>): Promise<void> {
    const paymentId = data.payment_id;
    if (paymentId) {
      await this.updatePaymentStatus(
        paymentId,
        PaymentStatus.SUCCESS,
        { webhook_data: data }
      );
      console.log(`Payment ${paymentId} marked as successful`);
    }
  }

  private async handlePaymentFailed(data: Record<string, any>): Promise<void> {
    const paymentId = data.payment_id;
    if (paymentId) {
      await this.updatePaymentStatus(
        paymentId,
        PaymentStatus.FAILED,
        { webhook_data: data, error: data.error }
      );
      console.log(`Payment ${paymentId} marked as failed`);
    }
  }

  private async handleSubscriptionActive(data: Record<string, any>): Promise<void> {
    const subscriptionId = data.subscription_id;
    if (subscriptionId) {
      await this.updateSubscriptionStatus(
        subscriptionId,
        SubscriptionStatus.ACTIVE,
        { webhook_data: data, current_period_end: data.current_period_end }
      );
      console.log(`Subscription ${subscriptionId} activated`);
    }
  }

  private async handleSubscriptionOnHold(data: Record<string, any>): Promise<void> {
    const subscriptionId = data.subscription_id;
    if (subscriptionId) {
      await this.updateSubscriptionStatus(
        subscriptionId,
        SubscriptionStatus.ON_HOLD,
        { webhook_data: data }
      );
      console.log(`Subscription ${subscriptionId} on hold`);
    }
  }

  private async handleSubscriptionFailed(data: Record<string, any>): Promise<void> {
    const subscriptionId = data.subscription_id;
    if (subscriptionId) {
      await this.updateSubscriptionStatus(
        subscriptionId,
        SubscriptionStatus.FAILED,
        { webhook_data: data }
      );
      console.log(`Subscription ${subscriptionId} failed`);
    }
  }

  private async handleSubscriptionRenewed(data: Record<string, any>): Promise<void> {
    const subscriptionId = data.subscription_id;
    if (subscriptionId) {
      await this.updateSubscriptionStatus(
        subscriptionId,
        SubscriptionStatus.ACTIVE,
        { webhook_data: data, current_period_end: data.current_period_end }
      );
      console.log(`Subscription ${subscriptionId} renewed`);
    }
  }

  private async handleSubscriptionPlanChanged(data: Record<string, any>): Promise<void> {
    const subscriptionId = data.subscription_id;
    if (subscriptionId) {
      await this.updateSubscriptionStatus(
        subscriptionId,
        SubscriptionStatus.ACTIVE,
        {
          webhook_data: data,
          previous_plan: data.previous_plan,
          new_plan: data.new_plan,
          current_period_end: data.current_period_end
        }
      );
      console.log(`Subscription ${subscriptionId} plan changed`);
    }
  }
}

// Export singleton instance
export const dodoPaymentsService = new DodoPaymentsService();