- Do not use dodo payments mcp for payment integration.
- Do not ever use mock data, always do real implemenetation, you are not allowed to create a fake mock of anything.
I have connected this app to pixelkeywording.com domain and hosted it on netlify.
Supabase details:
- you have access to supabase(meta) mcp which have sql rewad ability, and since the mcp can't run sql, you need to provide the sql to me to run and i will run it for you.
I have connected this app to pixelkeywording.com domain and hosted it on netlify.
Supabase details:
use PostgresTmeta mcp for supabase.