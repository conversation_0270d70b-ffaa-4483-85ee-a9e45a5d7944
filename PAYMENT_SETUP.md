# Payment System Setup & Troubleshooting

## 🚨 Quick Fix for Current Issues

### Issue 1: Authentication Session Not Persisting
**Problem**: Users get redirected to login after page refresh on `/app/payment-debug`

**Solution**: This is likely due to multiple authentication systems running. For now, after logging in:
1. Go directly to `/app/payment-debug` 
2. If redirected to login, log in again
3. Immediately navigate to `/app/payment-debug` without refreshing

### Issue 2: Payment API Returning 501 Error
**Problem**: `POST /api/payments/test/checkout` returns "501 Unsupported method"

**Root Cause**: Backend server is not running. The frontend (port 5174) is trying to call API endpoints that only exist on the backend server (port 3000).

**Solution**: Run both servers simultaneously.

## 🛠️ Correct Development Setup

### Option 1: Run Both Servers (Recommended)
```bash
# This starts both frontend (5174) and backend (3000) servers
npm run dev
```

### Option 2: Run Servers Separately
```bash
# Terminal 1: Start backend server
npm run dev:backend

# Terminal 2: Start frontend server  
npm run dev:frontend
```

### Verify Setup
1. Frontend should be at: http://localhost:5174
2. Backend should be at: http://localhost:3000
3. Test backend health: http://localhost:3000/api/health

## 🧪 Testing the Payment System

### 1. Test API Endpoints
```bash
# Test if backend is running and endpoints work
node scripts/test-api.js
```

### 2. Use Payment Debug Page
1. Go to `/app/payment-debug`
2. Click "Test Payment API Endpoint" to verify the API works
3. Click "Test Payment Webhook" to simulate a successful payment
4. Check if credits are added to your account

### 3. Test Real Payment Flow
1. Go to `/app/payment-test`
2. Try the subscription checkout
3. Complete payment in test mode
4. Should redirect to payment success page
5. Credits should be automatically added

## 🔧 What Was Fixed

### 1. Missing Payment Pages ✅
- Added `/payment-success` page
- Added `/payment-cancel` page  
- Added proper routing

### 2. Webhook Credit Processing ✅
- Fixed webhook handlers to actually add credits
- Added proper error handling and logging
- Credit calculation: **$1 = 10 credits**

### 3. Debug Tools ✅
- Added `/app/payment-debug` page for testing
- Added API endpoint testing
- Added webhook simulation
- Added profile information display

### 4. Development Setup ✅
- Updated npm scripts for easier development
- Added API proxy configuration
- Added test scripts

## 🐛 Troubleshooting

### "501 Unsupported method" Error
- **Cause**: Backend server not running
- **Fix**: Run `npm run dev` (not `npm run dev:frontend`)

### "Authentication Required" or Redirect to Login
- **Cause**: Multiple auth systems or session conflicts
- **Temporary Fix**: Log in again and navigate directly to the page
- **Long-term Fix**: Consolidate authentication systems (future task)

### Webhook Not Adding Credits
- **Check**: Go to `/app/payment-debug` and test webhook
- **Verify**: Check Supabase database for credit updates
- **Debug**: Look at browser console and server logs

### Payment Checkout Not Working
1. Verify both servers are running
2. Check `/app/payment-debug` API test
3. Verify Dodo Payments API keys in `.env`
4. Check browser network tab for failed requests

## 📊 Environment Variables

Make sure these are set in your `.env` file:
```env
# Dodo Payments
VITE_DODO_PAYMENTS_API_KEY=your_api_key
VITE_DODO_PAYMENTS_WEBHOOK_SECRET=your_webhook_secret
VITE_DODO_PAYMENTS_MODE=test

# Server-side (no VITE_ prefix)
DODO_PAYMENTS_API_KEY=your_api_key
DODO_PAYMENTS_WEBHOOK_SECRET=your_webhook_secret

# Supabase
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
```

## 🎯 Next Steps

1. **Test the current fixes**:
   - Run `npm run dev` 
   - Test payment flow end-to-end
   - Verify credits are added

2. **If still having issues**:
   - Use `/app/payment-debug` to diagnose
   - Check server logs for errors
   - Verify environment variables

3. **Future improvements**:
   - Consolidate authentication systems
   - Add better error handling
   - Improve user feedback

## 📞 Support

If you're still having issues:
1. Check the debug page: `/app/payment-debug`
2. Run the API test: `node scripts/test-api.js`
3. Check browser console for errors
4. Check server logs for backend errors
