/**
 * Development server starter
 * This script starts both the frontend and backend servers
 */
import { spawn } from 'child_process';
import { createServer } from 'net';
import path from 'path';

// Function to check if port is available
function isPortAvailable(port) {
  return new Promise((resolve) => {
    const server = createServer();
    server.listen(port, () => {
      server.close(() => resolve(true));
    });
    server.on('error', () => resolve(false));
  });
}

// Function to find available port
async function findAvailablePort(startPort) {
  let port = startPort;
  while (!(await isPortAvailable(port))) {
    console.log(`Port ${port} is in use, trying ${port + 1}...`);
    port++;
  }
  return port;
}

async function startServers() {
  try {
    // Find available port for backend
    const backendPort = await findAvailablePort(3001);
    console.log(`✅ Found available backend port: ${backendPort}`);

    // Start the backend server
    console.log(`🚀 Starting backend server on port ${backendPort}...`);
    const backend = spawn('node', ['src/server/server.cjs'], {
      stdio: 'inherit',
      shell: true,
      env: { ...process.env, PORT: backendPort }
    });

    // Wait a moment for backend to start
    console.log('⏳ Waiting for backend to initialize...');
    await new Promise(resolve => setTimeout(resolve, 3000));

    // Start the frontend server with the backend port
    console.log('🚀 Starting frontend server...');
    const frontend = spawn('npm', ['run', 'dev:frontend'], {
      stdio: 'inherit',
      shell: true,
      env: { ...process.env, VITE_BACKEND_PORT: backendPort }
    });

    // Handle process termination
    process.on('SIGINT', () => {
      console.log('\n🛑 Shutting down servers...');
      backend.kill('SIGINT');
      frontend.kill('SIGINT');
      process.exit(0);
    });

    // Handle backend server exit
    backend.on('exit', (code) => {
      console.log(`❌ Backend server exited with code ${code}`);
      frontend.kill('SIGINT');
      process.exit(code);
    });

    // Handle frontend server exit
    frontend.on('exit', (code) => {
      console.log(`❌ Frontend server exited with code ${code}`);
      backend.kill('SIGINT');
      process.exit(code);
    });

    console.log('\n🎉 Development servers started successfully!');
    console.log('📍 URLs:');
    console.log(`   - Frontend: http://localhost:5174`);
    console.log(`   - Backend:  http://localhost:${backendPort}`);
    console.log(`   - API Test: http://localhost:${backendPort}/api/health`);
    console.log('   - Debug:    http://localhost:5174/app/payment-debug');

  } catch (error) {
    console.error('❌ Error starting servers:', error);
    process.exit(1);
  }
}

// Start the servers
startServers();
