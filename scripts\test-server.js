#!/usr/bin/env node

/**
 * Simple server test to debug the backend issues
 */

const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');

const app = express();
const PORT = 3003; // Use a different port to avoid conflicts

// Middleware
app.use(cors());
app.use(bodyParser.json());

// Simple test routes
app.get('/api/health', (req, res) => {
  console.log('Health check requested');
  res.json({ 
    status: 'ok', 
    message: 'Test server is working',
    timestamp: new Date().toISOString() 
  });
});

app.get('/api/payments/health', (req, res) => {
  console.log('Payment health check requested');
  res.json({ 
    status: 'ok', 
    service: 'payments',
    message: 'Payment API is working',
    timestamp: new Date().toISOString() 
  });
});

app.post('/api/payments/test/checkout', (req, res) => {
  console.log('Test checkout requested:', req.body);
  res.json({
    success: true,
    message: 'Test checkout endpoint working',
    data: req.body,
    timestamp: new Date().toISOString()
  });
});

// Error handling
app.use((err, req, res, next) => {
  console.error('Server error:', err);
  res.status(500).json({ 
    error: 'Internal server error',
    message: err.message 
  });
});

// Start server
app.listen(PORT, '127.0.0.1', () => {
  console.log(`🚀 Test server running on http://127.0.0.1:${PORT}`);
  console.log('📍 Test endpoints:');
  console.log(`   - Health: http://127.0.0.1:${PORT}/api/health`);
  console.log(`   - Payment Health: http://127.0.0.1:${PORT}/api/payments/health`);
  console.log(`   - Test Checkout: http://127.0.0.1:${PORT}/api/payments/test/checkout`);
});

// Handle process termination
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down test server...');
  process.exit(0);
});

process.on('uncaughtException', (err) => {
  console.error('💥 Uncaught Exception:', err);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('💥 Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});
