// Test script to simulate subscription.active webhook event
const fetch = require('node-fetch');
const crypto = require('crypto');

// Configuration
const WEBHOOK_URL = 'http://127.0.0.1:3000/api/webhooks/dodo-payments';
const WEBHOOK_SECRET = process.env.DODO_PAYMENTS_WEBHOOK_SECRET || 'whsec_WCNk54MC15TS8FZ8eVIFZk1K';

// Create subscription.updated event with user's actual data
const subscriptionActiveEvent = {
  type: 'subscription.updated',
  data: {
      subscription_id: 'sub_ogmLSg8nAKte6tMEGpMaL', // From payment logs
      customer_id: 'cus_vx9mrMz2CURk4yqwcnDfX', // From payment logs
      status: 'active',
      current_period_end: Math.floor(Date.now() / 1000) + (30 * 24 * 60 * 60), // 30 days from now
      current_period_start: Math.floor(Date.now() / 1000),
      metadata: {
        user_email: '<EMAIL>',
        user_id: 'ba40622d-eaae-4437-838a-28d3910360b4',
        profile_id: 'ba40622d-eaae-4437-838a-28d3910360b4',
        plan_id: 'basic'
      }
    }
};

// Function to create webhook signature
function createWebhookSignature(payload, secret) {
  const webhookId = 'wh_' + crypto.randomBytes(8).toString('hex');
  const timestamp = Math.floor(Date.now() / 1000).toString();
  const stringToSign = `${webhookId}.${timestamp}.${JSON.stringify(payload)}`;
  
  const signature = crypto
    .createHmac('sha256', secret)
    .update(stringToSign)
    .digest('hex');
  
  return {
    id: webhookId,
    timestamp: timestamp,
    signature: signature
  };
}

// Function to send webhook event
async function sendWebhookEvent() {
  const webhookData = createWebhookSignature(subscriptionActiveEvent, WEBHOOK_SECRET);
  
  try {
    console.log('Sending subscription.active webhook event...');
    console.log('Event data:', JSON.stringify(subscriptionActiveEvent, null, 2));
    
    const response = await fetch(WEBHOOK_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'webhook-signature': webhookData.signature,
        'webhook-id': webhookData.id,
        'webhook-timestamp': webhookData.timestamp
      },
      body: JSON.stringify(subscriptionActiveEvent)
    });

    const responseText = await response.text();
    console.log(`Response (${response.status}):`, responseText);
    
    if (response.ok) {
      console.log('✅ Webhook sent successfully!');
    } else {
      console.log('❌ Webhook failed!');
    }
  } catch (error) {
    console.error('Error sending webhook event:', error);
  }
}

// Run the test
sendWebhookEvent().catch(console.error);