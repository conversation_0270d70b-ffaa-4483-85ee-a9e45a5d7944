/**
 * Server entry point
 * This file starts the Express server for handling API requests
 */
const http = require('http');
const path = require('path');
const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');
const dotenv = require('dotenv');

// __dirname is already available in CommonJS

// Load environment variables
console.log('Loading environment variables...');
dotenv.config({ path: path.join(__dirname, '../../.env') });
console.log('Environment variables loaded.');

// Import API routes
console.log('Loading payments routes...');
const paymentsRoutes = require('../routes/payments.cjs');
console.log('Payments routes loaded.');

console.log('Loading webhook handler...');
const webhookHandler = require('./webhooks/dodoPayments.cjs');
console.log('Webhook handler loaded.');

// Create Express app
const app = express();
const PORT = process.env.PORT || 3002;

// Middleware
app.use(cors());
app.use(bodyParser.json());

// Root route
app.get('/', (req, res) => {
  res.json({ 
    message: 'Meta Generation Tool API Server',
    status: 'running',
    endpoints: {
      health: '/api/health',
      payments: '/api/payments/*'
    }
  });
});

// API routes
console.log('Applying payments routes...');
app.use('/api/payments', paymentsRoutes);
console.log('Payments routes applied.');

// Webhook routes
console.log('Applying webhook routes...');
app.post('/api/webhooks/dodo-payments', webhookHandler);
console.log('Webhook routes applied.');

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

// Test endpoint for debugging
app.get('/api/test', (req, res) => {
  res.json({ 
    message: 'Test endpoint working',
    environment: {
      nodeEnv: process.env.NODE_ENV,
      supabaseUrl: process.env.SUPABASE_URL ? 'configured' : 'missing',
      dodoApiKey: process.env.DODO_PAYMENTS_API_KEY ? 'configured' : 'missing'
    },
    timestamp: new Date().toISOString()
  });
});

// Static files disabled for debugging
// if (process.env.NODE_ENV === 'production') {
//   app.use(express.static(path.join(__dirname, '../../dist')));
// }

// Create HTTP server
const server = http.createServer(app);

// Add error handling
server.on('error', (err) => {
  console.error('Server error:', err);
  if (err.code === 'EADDRINUSE') {
    console.error(`Port ${PORT} is already in use`);
  }
  process.exit(1);
});

process.on('uncaughtException', (err) => {
  console.error('Uncaught Exception:', err);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

process.on('exit', (code) => {
  console.log(`Process is exiting with code: ${code}`);
});

// Start server
try {
  server.listen(PORT, '127.0.0.1', (err) => {
  if (err) {
    console.error('Failed to start server:', err);
    process.exit(1);
  }
    console.log(`Server running on http://127.0.0.1:${PORT}`);
  });
} catch (err) {
  console.error('Error during server startup:', err);
  process.exit(1);
}

module.exports = server;
