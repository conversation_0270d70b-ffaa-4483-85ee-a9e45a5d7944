/**
 * Manual webhook test script to simulate Dodo Payments webhook events
 * This will help us test if the webhook processing works correctly
 */

const crypto = require('crypto');

// Configuration
const WEBHOOK_URL = 'http://localhost:3001/api/webhooks/dodo-payments';
const WEBHOOK_SECRET = 'whsec_WCNk54MC15TS8FZ8eVIFZk1K'; // From .env file
const PROFILE_ID = 'ba40622d-eaae-4437-838a-28d3910360b4'; // The user ID from your test

// Create a test payment.succeeded event
const testPaymentEvent = {
  type: 'payment.succeeded',
  data: {
    id: 'pay_test_' + Date.now(),
    customerId: 'cus_omiAFdLPrCz1uWiJnZxWU',
    amount: 1000, // $10.00 in cents
    currency: 'USD',
    status: 'succeeded',
    metadata: {
      profile_id: PROFILE_ID,
      user_id: PROFILE_ID,
      user_email: '<EMAIL>',
      test: 'true',
      payment_type: 'api_direct',
      timestamp: new Date().toISOString()
    }
  }
};

// Create a test subscription.active event
const testSubscriptionEvent = {
  type: 'subscription.active',
  data: {
    subscription_id: 'sub_qR9BFfuLiMU4L66o5IVja',
    customer_id: 'cus_omiAFdLPrCz1uWiJnZxWU',
    status: 'active',
    current_period_start: Math.floor(Date.now() / 1000),
    current_period_end: Math.floor(Date.now() / 1000) + (30 * 24 * 60 * 60), // 30 days from now
    metadata: {
      profile_id: PROFILE_ID,
      user_id: PROFILE_ID,
      user_email: '<EMAIL>',
      plan_id: 'basic',
      test: 'true'
    }
  }
};

/**
 * Create webhook signature for Dodo Payments
 */
function createWebhookSignature(payload, secret) {
  const timestamp = Math.floor(Date.now() / 1000).toString();
  const webhookId = 'wh_' + crypto.randomBytes(16).toString('hex');
  
  // Create string to sign: id.timestamp.payload
  const stringToSign = `${webhookId}.${timestamp}.${payload}`;
  
  // Create HMAC signature
  const signature = crypto
    .createHmac('sha256', secret)
    .update(stringToSign)
    .digest('hex');
  
  return {
    signature,
    timestamp,
    webhookId
  };
}

/**
 * Send webhook event to the server using Node.js http module
 */
function sendWebhookEvent(event, eventName) {
  return new Promise((resolve) => {
    try {
      console.log(`\n🧪 Testing ${eventName} webhook event...`);
      console.log('📍 Webhook URL:', WEBHOOK_URL);
      console.log('👤 Profile ID:', PROFILE_ID);
      console.log('📦 Event data:', JSON.stringify(event, null, 2));

      const payload = JSON.stringify(event);
      const { signature, timestamp, webhookId } = createWebhookSignature(payload, WEBHOOK_SECRET);

      console.log('\n🔐 Webhook signature details:');
      console.log('  - Signature:', signature);
      console.log('  - Timestamp:', timestamp);
      console.log('  - Webhook ID:', webhookId);

      const http = require('http');
      const url = require('url');

      const parsedUrl = url.parse(WEBHOOK_URL);

      const options = {
        hostname: parsedUrl.hostname,
        port: parsedUrl.port,
        path: parsedUrl.path,
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Content-Length': Buffer.byteLength(payload),
          'webhook-signature': signature,
          'webhook-id': webhookId,
          'webhook-timestamp': timestamp
        }
      };

      const req = http.request(options, (res) => {
        let responseData = '';

        res.on('data', (chunk) => {
          responseData += chunk;
        });

        res.on('end', () => {
          console.log('\n📤 Webhook response:');
          console.log('  - Status:', res.statusCode, res.statusMessage);
          console.log('  - Response:', responseData);

          if (res.statusCode >= 200 && res.statusCode < 300) {
            console.log('✅ Webhook processed successfully!');
            resolve(true);
          } else {
            console.log('❌ Webhook processing failed!');
            resolve(false);
          }
        });
      });

      req.on('error', (error) => {
        console.error('💥 Error sending webhook:', error.message);
        resolve(false);
      });

      req.write(payload);
      req.end();

    } catch (error) {
      console.error('💥 Error sending webhook:', error.message);
      resolve(false);
    }
  });
}

/**
 * Main test function
 */
async function runWebhookTests() {
  console.log('🚀 Starting Dodo Payments Webhook Tests');
  console.log('=' .repeat(50));
  
  // Test 1: Payment succeeded event
  const paymentSuccess = await sendWebhookEvent(testPaymentEvent, 'payment.succeeded');
  
  // Wait a bit between tests
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // Test 2: Subscription active event
  const subscriptionSuccess = await sendWebhookEvent(testSubscriptionEvent, 'subscription.active');
  
  console.log('\n' + '=' .repeat(50));
  console.log('📊 Test Results:');
  console.log(`  - Payment webhook: ${paymentSuccess ? '✅ PASSED' : '❌ FAILED'}`);
  console.log(`  - Subscription webhook: ${subscriptionSuccess ? '✅ PASSED' : '❌ FAILED'}`);
  
  if (paymentSuccess && subscriptionSuccess) {
    console.log('\n🎉 All webhook tests passed! Check your Supabase database for updated credits and subscription status.');
  } else {
    console.log('\n⚠️  Some webhook tests failed. Check the server logs for more details.');
  }
}

// Run the tests
runWebhookTests().catch(console.error);
