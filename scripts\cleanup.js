#!/usr/bin/env node

/**
 * Cleanup script to kill running development servers
 */

import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

async function cleanup() {
  console.log('🧹 Cleaning up running development servers...\n');

  try {
    // Kill all node processes (Windows)
    if (process.platform === 'win32') {
      console.log('Killing Node.js processes...');
      await execAsync('taskkill /f /im node.exe').catch(() => {
        console.log('No Node.js processes found to kill');
      });
    } else {
      // Kill processes on Unix-like systems
      console.log('Killing Node.js processes...');
      await execAsync('pkill -f node').catch(() => {
        console.log('No Node.js processes found to kill');
      });
    }

    // Check what's using common ports
    console.log('\nChecking port usage...');
    
    const ports = [3000, 3001, 5174];
    for (const port of ports) {
      try {
        if (process.platform === 'win32') {
          const { stdout } = await execAsync(`netstat -ano | findstr :${port}`);
          if (stdout.trim()) {
            console.log(`Port ${port} is in use:`);
            console.log(stdout);
          } else {
            console.log(`✅ Port ${port} is free`);
          }
        } else {
          const { stdout } = await execAsync(`lsof -ti:${port}`);
          if (stdout.trim()) {
            console.log(`Port ${port} is in use by PID: ${stdout.trim()}`);
            // Kill the process
            await execAsync(`kill -9 ${stdout.trim()}`);
            console.log(`✅ Killed process on port ${port}`);
          } else {
            console.log(`✅ Port ${port} is free`);
          }
        }
      } catch (error) {
        console.log(`✅ Port ${port} is free`);
      }
    }

    console.log('\n🎉 Cleanup completed!');
    console.log('You can now run: npm run dev');
    
  } catch (error) {
    console.error('❌ Error during cleanup:', error.message);
  }
}

cleanup();
