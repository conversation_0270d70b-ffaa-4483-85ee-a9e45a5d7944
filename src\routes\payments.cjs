/**
 * Payment API Routes (JavaScript version)
 * Handles payment-related API requests with actual Dodo Payments integration
 */

const { Router } = require('express');
const axios = require('axios');

const router = Router();

// Dodo Payments configuration
const DODO_API_KEY = process.env.DODO_PAYMENTS_API_KEY || 'Y5h-0edhftQ-_aWv.UDaLs-NfZ0DRsjzWCVZztJH_9xCF9UL7dHbe34fZZyDQd6Ij';
const DODO_API_URL = (process.env.DODO_PAYMENTS_API_URL || 'https://test.dodopayments.com').replace(/`/g, '').trim();

// Configure axios for Dodo Payments API
const dodoApi = axios.create({
  baseURL: DODO_API_URL,
  headers: {
    'Authorization': `Bearer ${DODO_API_KEY}`,
    'Content-Type': 'application/json'
  }
});

/**
 * Create checkout session
 * POST /api/payments/checkout
 */
router.post('/checkout', async (req, res) => {
  try {
    const { 
      billing_currency,
      allowed_payment_method_types,
      product_cart,
      return_url,
      customer,
      metadata 
    } = req.body;

    // Validate required fields
    if (!product_cart || !Array.isArray(product_cart) || product_cart.length === 0) {
      return res.status(400).json({ error: 'Product cart is required' });
    }

    // Validate amount in product cart
    const totalAmount = product_cart.reduce((sum, item) => sum + (item.amount || 0), 0);
    if (totalAmount <= 0) {
      return res.status(400).json({ error: 'Valid amount is required' });
    }

    // Prepare payment data for Dodo Payments API according to documentation
    const paymentData = {
      payment_link: true,
      billing: {
        city: 'Test City',
        country: 'US',
        state: 'CA',
        street: '123 Test St',
        zipcode: '12345'
      },
      customer: customer || {
        email: '<EMAIL>',
        name: 'Test User'
      },
      product_cart: product_cart.map(item => ({
        product_id: item.product_id || 'pdt_iSJretaAUZA4a1ZeIBjMQ',
        quantity: item.quantity || 1
      })),
      billing_currency: billing_currency || 'USD',
      allowed_payment_method_types: allowed_payment_method_types || ['credit', 'debit'],
      return_url: return_url || `${req.protocol}://${req.get('host')}/payment-success`,
      webhook_url: process.env.DODO_PAYMENTS_WEBHOOK_URL || 'http://localhost:3001/api/webhooks/dodo-payments',
      metadata: metadata || {}
    };

    // Make API request to Dodo Payments - Create One Time Payment
    const response = await dodoApi.post('/payments', paymentData);
    
    return res.status(200).json({
      id: response.data.id,
      url: response.data.url,
      checkout_url: response.data.checkout_url,
      status: response.data.status,
      expires_at: response.data.expires_at
    });
  } catch (error) {
    console.error('Error creating checkout session:', error);
    return res.status(500).json({ 
      error: 'Failed to create checkout session',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Get payment details
 * GET /api/payments/:paymentId
 */
router.get('/:paymentId', async (req, res) => {
  try {
    const { paymentId } = req.params;
    
    if (!paymentId) {
      return res.status(400).json({ error: 'Payment ID is required' });
    }

    // Make API request to Dodo Payments - Get Payment Detail
    const response = await dodoApi.get(`/payments/${paymentId}`);
    
    // Handle 404 from API
    if (response.status === 404) {
      return res.status(404).json({ error: 'Payment not found' });
    }
    
    return res.status(200).json(response.data);
  } catch (error) {
    console.error('Error retrieving payment:', error);
    return res.status(500).json({ 
      error: 'Failed to retrieve payment',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Create customer
 * POST /api/payments/customers
 */
router.post('/customers', async (req, res) => {
  try {
    const { email, name, metadata } = req.body;
    
    if (!email) {
      return res.status(400).json({ error: 'Email is required' });
    }

    const customerData = {
      email,
      name: name || '',
      metadata: metadata || {}
    };

    // Make API request to Dodo Payments
    const response = await dodoApi.post('/customers', customerData);
    return res.status(200).json(response.data);
  } catch (error) {
    console.error('Error creating customer:', error);
    return res.status(500).json({ 
      error: 'Failed to create customer',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Test endpoint for checkout (no authentication required)
 * POST /api/payments/test/checkout
 */
router.post('/test/checkout', async (req, res) => {
  try {
    // Hardcoded test data to ensure a valid request (subscription format)
    const paymentData = {
      payment_link: true,
      billing: {
        city: 'Test City',
        country: 'US',
        state: 'CA',
        street: '123 Test St',
        zipcode: '12345'
      },
      customer: {
        email: req.body.user_email || '<EMAIL>',
        name: req.body.user_name || 'Test User'
      },
      product_id: 'pdt_iSJretaAUZA4a1ZeIBjMQ',
      quantity: 1,
      webhook_url: process.env.DODO_PAYMENTS_WEBHOOK_URL || 'http://localhost:3001/api/webhooks/dodo-payments',
      metadata: {
        test: 'true',
        user_id: req.body.user_id || 'ba40622d-eaae-4437-838a-28d3910360b4',
        profile_id: req.body.profile_id || req.body.user_id || 'ba40622d-eaae-4437-838a-28d3910360b4',
        user_email: req.body.user_email || '<EMAIL>',
        payment_type: 'api_direct',
        timestamp: new Date().toISOString()
      }
    };

    console.log('Sending payment data to Dodo:', JSON.stringify(paymentData, null, 2));

    // Make API request to Dodo Payments (use subscriptions endpoint for recurring products)
    const response = await dodoApi.post('/subscriptions', paymentData);

    console.log('✅ Success! Dodo Payments response:', JSON.stringify(response.data, null, 2));

    return res.status(200).json({
      id: response.data.id,
      subscription_id: response.data.subscription_id,
      payment_id: response.data.payment_id,
      url: response.data.url,
      checkout_url: response.data.checkout_url,
      payment_link: response.data.payment_link,
      status: response.data.status,
      expires_at: response.data.expires_at,
      // Include the full response for debugging
      _debug_full_response: response.data
    });
  } catch (error) {
    console.error('Error creating test checkout session:', error);
    return res.status(500).json({ 
      error: 'Failed to create test checkout session',
      details: error.message,
      ...(error.response && { dodo_response: error.response.data })
    });
  }
});

/**
 * Test endpoint for simple payment (similar to Python backend)
 * POST /api/payments/test/simple-payment
 */
router.post('/test/simple-payment', async (req, res) => {
  try {
    const testPaymentData = {
      payment_link: true,
      billing: {
        city: 'Test City',
        country: 'US',
        state: 'CA',
        street: '123 Test St',
        zipcode: '12345'
      },
      customer: {
        email: '<EMAIL>',
        name: 'Test User'
      },
      product_cart: [
        {
          product_id: 'pdt_iSJretaAUZA4a1ZeIBjMQ',
          quantity: 1
        }
      ],
      billing_currency: 'USD',
      allowed_payment_method_types: ['credit', 'debit'],
      return_url: `${req.protocol}://${req.get('host')}/payment-success`,
      metadata: {
        test: true,
        source: 'test_endpoint',
        timestamp: new Date().toISOString()
      }
    };

    // Make API request to Dodo Payments
    const response = await dodoApi.post('/payments', testPaymentData);
    
    return res.status(200).json({
      id: response.data.id,
      url: response.data.url,
      checkout_url: response.data.checkout_url,
      status: response.data.status,
      expires_at: response.data.expires_at
    });
  } catch (error) {
    console.error('Error creating test payment:', error);
    return res.status(500).json({ 
      error: 'Failed to create test payment',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Health check for payments
 * GET /api/payments/health
 */
router.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    service: 'payments',
    dodo_payments: {
      api_key_configured: !!DODO_API_KEY,
      api_url: DODO_API_URL
    },
    timestamp: new Date().toISOString()
  });
});

/**
 * Manual payment completion endpoint
 * POST /api/payments/test/complete-payment
 */
router.post('/test/complete-payment', async (req, res) => {
  try {
    const { payment_id, subscription_id, user_id, profile_id, amount = 1000 } = req.body;

    console.log('🔄 Manually completing payment:', { payment_id, subscription_id, user_id, profile_id, amount });

    // Import Supabase client
    const { createClient } = require('@supabase/supabase-js');
    const supabaseUrl = 'https://alywdwwqrtddplqsbksd.supabase.co';
    const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.kiuDTgrGVi4rbZ3XYSIfqTTsiNUCvByDo5aDuXkwsZQ';
    const supabase = createClient(supabaseUrl, supabaseKey);

    // 1. Log the payment
    const { data: paymentResult, error: paymentError } = await supabase
      .from('payments')
      .insert({
        user_id: profile_id || user_id,
        customer_id: 'cus_omiAFdLPrCz1uWiJnZxWU', // Updated to latest customer ID
        amount: amount,
        status: 'succeeded',
        payment_id: payment_id,
        metadata: {
          subscription_id: subscription_id,
          payment_type: 'manual_completion',
          test: 'true'
        }
      })
      .select();

    if (paymentError) {
      console.error('❌ Error logging payment:', paymentError);
    } else {
      console.log('✅ Payment logged:', paymentResult);
    }

    // 2. Add credits to user (10 credits per dollar)
    const creditsToAdd = Math.floor(amount / 100 * 10);
    console.log(`💰 Adding ${creditsToAdd} credits to profile ${profile_id || user_id}`);

    const { data: creditResult, error: creditError } = await supabase.rpc('add_credits', {
      user_id: profile_id || user_id,
      amount: creditsToAdd,
      credit_type: 'purchase',
      description: `Manual payment completion - ${amount/100} USD purchase`
    });

    if (creditError) {
      console.error('❌ Error adding credits:', creditError);
    } else {
      console.log('✅ Credits added successfully:', creditResult);
    }

    // 3. Update subscription status if subscription_id provided
    let subscriptionError = null;
    let subscriptionResult = null;

    if (subscription_id) {
      const result = await supabase
        .from('profiles')
        .update({
          subscription_plan: 'basic',
          subscription_id: subscription_id,
          subscription_start_date: new Date().toISOString(),
          subscription_end_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days from now
          customer_id: 'cus_omiAFdLPrCz1uWiJnZxWU' // Update to latest customer ID
        })
        .eq('id', profile_id || user_id)
        .select();

      subscriptionError = result.error;
      subscriptionResult = result.data;

      if (subscriptionError) {
        console.error('❌ Error updating subscription:', subscriptionError);
      } else {
        console.log('✅ Subscription updated:', subscriptionResult);
      }
    }

    return res.status(200).json({
      success: true,
      message: 'Payment completed manually',
      payment_logged: !paymentError,
      credits_added: !creditError,
      subscription_updated: subscription_id ? !subscriptionError : false,
      credits_added_amount: creditsToAdd
    });

  } catch (error) {
    console.error('Error completing payment manually:', error);
    return res.status(500).json({
      error: 'Failed to complete payment manually',
      details: error.message || 'Unknown error'
    });
  }
});

/**
 * Test webhook simulation endpoint
 * POST /api/payments/test/webhook
 */
router.post('/test/webhook', async (req, res) => {
  try {
    const { user_id, profile_id, amount = 1000, event_type = 'payment.succeeded' } = req.body;

    console.log('🔄 Simulating webhook event:', { user_id, profile_id, amount, event_type });

    // Import the webhook handler
    const webhookHandler = require('../server/webhooks/dodoPayments.cjs');

    // Create a mock webhook payload
    const mockWebhookPayload = {
      type: event_type,
      data: {
        customer_id: 'cus_vx9mrMz2CURk4yqwcnDfX', // Use the known customer ID
        amount: amount,
        metadata: {
          profile_id: profile_id || user_id,
          user_id: user_id,
          user_email: '<EMAIL>',
          payment_type: 'test_simulation',
          test: 'true'
        }
      }
    };

    // Create a mock request object
    const mockRequest = {
      body: JSON.stringify(mockWebhookPayload),
      headers: {
        'dodo-signature': 'test-signature'
      }
    };

    // Create a mock response object
    let responseData = null;
    let statusCode = 200;
    const mockResponse = {
      status: (code) => {
        statusCode = code;
        return mockResponse;
      },
      json: (data) => {
        responseData = data;
        return mockResponse;
      }
    };

    // Call the webhook handler
    await webhookHandler(mockRequest, mockResponse);

    return res.status(200).json({
      success: true,
      message: 'Webhook simulation completed',
      webhook_status: statusCode,
      webhook_response: responseData,
      simulated_data: mockWebhookPayload
    });

  } catch (error) {
    console.error('Error simulating webhook:', error);
    return res.status(500).json({
      error: 'Failed to simulate webhook',
      details: error.message || 'Unknown error'
    });
  }
});

module.exports = router;