# netlify.toml
[build]
  command = "npm run build"
  publish = "dist"

# Handle Dodo Payments webhook
[[redirects]]
  from = "/api/webhooks/dodo-payments"
  to = "/.netlify/functions/dodo-payments-webhook"
  status = 200
  force = true

# Handle SPA routing
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

# Handle subdomain redirects
[[redirects]]
  from = "https://app.pixelkeywording.com/*"
  to = "https://pixelkeywording.com/:splat"
  status = 301
  force = true

# Handle auth callback
[[redirects]]
  from = "/auth/callback"
  to = "/index.html"
  status = 200
