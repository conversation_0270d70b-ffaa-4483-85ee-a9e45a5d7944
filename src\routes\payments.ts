import express from 'express';
import crypto from 'crypto';
import { requireAuth } from '../server/middleware/auth.js';
import { dodoPaymentsService } from '../services/dodoPayments';
import {
  CreatePaymentRequest,
  CreateSubscriptionRequest,
  WebhookEvent,
  PaymentStatus,
  SubscriptionStatus
} from '../types/payment';

// Error interface for better type safety
interface ApiError {
  message: string;
  code?: string;
  status?: number;
  stack?: string;
}

const router = express.Router();

// Create checkout session
router.post('/checkout', requireAuth, async (req, res) => {
  try {
    const { 
      amount, 
      currency = 'USD', 
      productId, 
      returnUrl,
      customer,
      billing,
      metadata 
    } = req.body;
    
    if (!amount || !productId) {
      return res.status(400).json({ error: 'Amount and productId are required' });
    }

    if (!returnUrl) {
      return res.status(400).json({ error: 'Return URL is required' });
    }

    const paymentRequest: CreatePaymentRequest = {
      billing_currency: currency,
      product_cart: [{
        product_id: productId,
        amount: amount,
        quantity: 1
      }],
      return_url: returnUrl,
      customer: customer || {
        customer_id: req.user?.id,
        email: req.user?.email,
        name: req.user?.name
      },
      billing: billing || {
        street: '123 Default Street',
        city: 'Default City',
        state: 'Default State',
        country: 'US',
        zipcode: '12345'
      },
      metadata: metadata || { user_id: req.user?.id }
    };

    const paymentResponse = await dodoPaymentsService.createPayment(
      paymentRequest,
      req.user?.id
    );

    res.json(paymentResponse);
  } catch (error: unknown) {
    const apiError = error as ApiError;
    console.error('Payment creation error:', apiError);
    res.status(500).json({ 
      error: 'Failed to create payment',
      details: apiError.message 
    });
  }
});

// Create subscription
router.post('/subscriptions', requireAuth, async (req, res) => {
  try {
    const {
      productId,
      customer,
      billing,
      quantity = 1,
      paymentLink = true,
      metadata
    } = req.body;

    if (!productId) {
      return res.status(400).json({ error: 'Product ID is required' });
    }

    if (!customer || !customer.customer_id || !customer.email) {
      return res.status(400).json({ error: 'Customer with customer_id and email is required' });
    }

    if (!billing) {
      return res.status(400).json({ error: 'Billing address is required' });
    }

    const subscriptionRequest: CreateSubscriptionRequest = {
      customer,
      product_id: productId,
      billing,
      quantity,
      payment_link: paymentLink,
      metadata: metadata || { user_id: req.user?.id }
    };

    const subscriptionResponse = await dodoPaymentsService.createSubscription(
      subscriptionRequest,
      req.user?.id
    );

    res.json(subscriptionResponse);
  } catch (error: unknown) {
    const apiError = error as ApiError;
    console.error('Subscription creation error:', apiError);
    res.status(500).json({ 
      error: 'Failed to create subscription',
      details: apiError.message 
    });
  }
});

// Get payment details
router.get('/:paymentId', requireAuth, async (req, res) => {
  try {
    const { paymentId } = req.params;
    
    const payment = await dodoPaymentsService.getPayment(paymentId);
    
    if (!payment) {
      return res.status(404).json({ error: 'Payment not found' });
    }

    res.json(payment);
  } catch (error: unknown) {
    const apiError = error as ApiError;
    console.error('Payment fetch error:', apiError);
    res.status(500).json({ 
      error: 'Failed to fetch payment details',
      details: apiError.message 
    });
  }
});

// List payments
router.get('/', requireAuth, async (req, res) => {
  try {
    // This would typically fetch from database with user filtering
    // For now, return empty array as the service doesn't have a list method
    res.json({
      payments: [],
      total: 0
    });
  } catch (error: unknown) {
    const apiError = error as ApiError;
    console.error('Payments list error:', apiError);
    res.status(500).json({ 
      error: 'Failed to fetch payments',
      details: apiError.message 
    });
  }
});

// Webhook endpoint for Dodo Payments
router.post('/webhooks/dodo', express.raw({ type: 'application/json' }), async (req, res) => {
  try {
    const body = req.body.toString();
    const webhookSignature = req.headers['dodo-signature'] as string;
    const webhookId = req.headers['dodo-webhook-id'] as string;
    const webhookTimestamp = req.headers['dodo-timestamp'] as string;

    if (!webhookSignature || !webhookId || !webhookTimestamp) {
      console.error('Missing webhook headers');
      return res.status(400).json({ error: 'Missing webhook headers' });
    }

    // Verify webhook signature
    const isValid = dodoPaymentsService.verifyWebhookSignature(
      body,
      webhookSignature,
      webhookId,
      webhookTimestamp
    );

    if (!isValid) {
      console.error('Invalid webhook signature');
      return res.status(401).json({ error: 'Invalid webhook signature' });
    }

    // Parse webhook event
    const eventData = JSON.parse(body);
    const event: WebhookEvent = {
      business_id: eventData.business_id,
      timestamp: eventData.timestamp,
      type: eventData.type,
      data: eventData.data
    };

    // Process webhook event
    await dodoPaymentsService.processWebhookEvent(event);

    res.json({ status: 'success' });
  } catch (error: unknown) {
    const apiError = error as ApiError;
    console.error('Error processing webhook:', apiError);
    res.status(500).json({ 
      error: 'Failed to process webhook',
      details: apiError.message 
    });
  }
});

// Test endpoint for simple payment creation
router.post('/test', requireAuth, async (req, res) => {
  try {
    const testPayment: CreatePaymentRequest = {
      billing_currency: 'USD',
      product_cart: [{
        product_id: 'pdt_iSJretaAUZA4a1ZeIBjMQ',
        amount: 1000, // $10.00 in cents
        quantity: 1
      }],
      return_url: 'http://localhost:3000/payment-success',
      customer: {
        customer_id: 'test_customer_123',
        email: '<EMAIL>',
        name: 'Test Customer'
      },
      billing: {
        street: '123 Test Street',
        city: 'Test City',
        state: 'TS',
        country: 'US',
        zipcode: '12345'
      },
      metadata: { test: 'true', source: 'api_test' }
    };

    const paymentResponse = await dodoPaymentsService.createPayment(
      testPayment,
      req.user?.id
    );

    res.json(paymentResponse);
  } catch (error: unknown) {
    const apiError = error as ApiError;
    console.error('Test payment error:', apiError);
    res.status(500).json({ 
      error: 'Test payment failed',
      details: apiError.message 
    });
  }
});

export default router;