// Basic HTTP test without node-fetch
const http = require('http');

// Create a simple HTTP server
const server = http.createServer((req, res) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);
  console.log('Headers:', req.headers);
  
  if (req.method === 'POST' && req.url === '/api/webhooks/dodo-payments') {
    let body = '';
    
    req.on('data', chunk => {
      body += chunk.toString();
    });
    
    req.on('end', () => {
      console.log('🔔 Webhook received!');
      console.log('Body:', body);
      
      res.writeHead(200, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify({ 
        received: true, 
        message: 'Webhook processed successfully',
        timestamp: new Date().toISOString()
      }));
    });
  } else if (req.method === 'GET' && req.url === '/health') {
    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({ status: 'ok', timestamp: new Date().toISOString() }));
  } else {
    res.writeHead(404, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({ error: 'Not found' }));
  }
});

const PORT = 3001; // Changed port
server.listen(PORT, '127.0.0.1', () => {
  console.log(`🚀 Basic HTTP server running on http://127.0.0.1:${PORT}`);
  console.log(`📡 Webhook endpoint: http://127.0.0.1:${PORT}/api/webhooks/dodo-payments`);
});

server.on('error', (err) => {
  console.error('Server error:', err);
});

// Keep the server alive
process.on('SIGINT', () => {
  console.log('\n🛑 Server shutting down...');
  server.close(() => {
    console.log('✅ Server closed');
    process.exit(0);
  });
});