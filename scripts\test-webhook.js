#!/usr/bin/env node

/**
 * Test script to simulate Dodo Payments webhook events
 * This helps verify that the webhook is properly processing payments and adding credits
 */

const crypto = require('crypto');

// Configuration - update these with your actual values
const WEBHOOK_URL = process.env.WEBHOOK_URL || 'http://localhost:8888/.netlify/functions/dodo-payments-webhook';
const WEBHOOK_SECRET = process.env.DODO_PAYMENTS_WEBHOOK_SECRET || 'whsec_WCNk54MC15TS8FZ8eVIFZk1K';

// Get profile ID from command line argument
const profileId = process.argv[2];
if (!profileId) {
  console.error('❌ Please provide a profile ID as an argument');
  console.error('Usage: node scripts/test-webhook.js <profile-id>');
  process.exit(1);
}

// Sample webhook event for successful payment
const testEvent = {
  type: 'payment.succeeded',
  data: {
    id: 'pay_' + crypto.randomBytes(8).toString('hex'),
    customerId: 'cus_' + crypto.randomBytes(8).toString('hex'),
    amount: 1000, // $10.00 in cents
    currency: 'USD',
    status: 'succeeded',
    metadata: {
      profile_id: profileId,
      credits: 100, // Explicitly specify credits to add
      test: true
    }
  },
  timestamp: Math.floor(Date.now() / 1000)
};

// Create webhook signature
function createWebhookSignature(payload, secret) {
  const timestamp = Math.floor(Date.now() / 1000);
  const webhookId = 'wh_' + crypto.randomBytes(8).toString('hex');
  
  // Create the signature payload
  const signaturePayload = `${webhookId}.${timestamp}.${payload}`;
  
  // Create HMAC signature
  const signature = crypto
    .createHmac('sha256', secret)
    .update(signaturePayload)
    .digest('hex');
  
  return {
    signature: `v1,${signature}`,
    timestamp: timestamp.toString(),
    webhookId
  };
}

async function testWebhook() {
  try {
    console.log('🧪 Testing Dodo Payments Webhook');
    console.log('📍 Webhook URL:', WEBHOOK_URL);
    console.log('👤 Profile ID:', profileId);
    console.log('💰 Test Payment Amount: $10.00 (100 credits)');
    console.log('');

    const payload = JSON.stringify(testEvent);
    const { signature, timestamp, webhookId } = createWebhookSignature(payload, WEBHOOK_SECRET);

    console.log('📤 Sending webhook event...');
    
    // Use dynamic import for fetch in Node.js
    const fetch = (await import('node-fetch')).default;
    
    const response = await fetch(WEBHOOK_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'dodo-signature': signature,
        'dodo-timestamp': timestamp,
        'dodo-webhook-id': webhookId
      },
      body: payload
    });

    const responseText = await response.text();
    
    if (response.ok) {
      console.log('✅ Webhook processed successfully!');
      console.log('📊 Response Status:', response.status);
      console.log('📄 Response Body:', responseText);
      console.log('');
      console.log('🎉 Test completed! Check your app to see if credits were added.');
    } else {
      console.error('❌ Webhook failed!');
      console.error('📊 Response Status:', response.status);
      console.error('📄 Response Body:', responseText);
    }
  } catch (error) {
    console.error('💥 Error testing webhook:', error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.error('');
      console.error('💡 Make sure your local development server is running:');
      console.error('   npm run dev');
      console.error('   or');
      console.error('   netlify dev');
    }
  }
}

// Run the test
testWebhook();
