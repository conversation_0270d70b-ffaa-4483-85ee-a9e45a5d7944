/**
 * Simple webhook test handler without signature verification
 */
const express = require('express');
const app = express();

// Middleware to parse JSON
app.use(express.json());

// Simple webhook endpoint
app.post('/api/webhooks/dodo-payments', (req, res) => {
  console.log('🔔 Webhook received!');
  console.log('Headers:', req.headers);
  console.log('Body:', req.body);
  
  // Just return success
  res.status(200).json({ 
    received: true, 
    message: 'Webhook processed successfully',
    timestamp: new Date().toISOString()
  });
});

// Health check
app.get('/health', (req, res) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

const PORT = process.env.PORT || 3000;
app.listen(PORT, '127.0.0.1', () => {
  console.log(`🚀 Simple webhook test server running on http://127.0.0.1:${PORT}`);
  console.log(`📡 Webhook endpoint: http://127.0.0.1:${PORT}/api/webhooks/dodo-payments`);
});