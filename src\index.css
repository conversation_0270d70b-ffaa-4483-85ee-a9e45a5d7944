@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;
 
@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --primary: 142.1 76.2% 36.3%;
    --primary-foreground: 355.7 100% 97.3%;
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 142.1 76.2% 36.3%;
    --radius: 0.5rem;
  }
 
  .dark {
    /* Enhanced dark theme colors inspired by Stockfill */
    --background: 240 10% 3%; /* Darker background */
    --foreground: 0 0% 98%;
    --card: 240 10% 5%; /* Slightly lighter than background for cards */
    --card-foreground: 0 0% 98%;
    --popover: 240 10% 5%;
    --popover-foreground: 0 0% 98%;
    --primary: 142.1 70.6% 45.3%; /* Vibrant green */
    --primary-foreground: 0 0% 100%;
    --secondary: 240 3.7% 12%;
    --secondary-foreground: 0 0% 98%;
    --muted: 240 3.7% 12%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 142.4 71.8% 29.2%;
  }
}
 
@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground font-sans;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

/* Enhanced custom styles for Stockfill-like UI */
.image-card {
  @apply relative overflow-hidden rounded-lg border border-border/40 bg-card/95 transition-all duration-200 hover:shadow-md dark:shadow-lg dark:shadow-black/10;
}

.image-card-selected {
  @apply border-primary/80 ring-1 ring-primary/30;
}

.sidebar-nav-item {
  @apply flex items-center gap-2 rounded-lg px-4 py-2.5 text-sm font-medium transition-all duration-200 hover:bg-secondary/80;
}

.sidebar-nav-item-active {
  @apply bg-primary text-white shadow-sm shadow-primary/25;
}

.metadata-panel {
  @apply space-y-4 rounded-lg bg-card/95 p-5 shadow-md dark:shadow-lg dark:shadow-black/10 backdrop-blur-sm;
}

.keyword-tag {
  @apply inline-flex items-center rounded-full bg-primary/10 dark:bg-primary/20 px-2.5 py-0.5 text-xs font-medium text-primary-foreground dark:text-primary-foreground m-0.5 transition-colors hover:bg-primary/20 dark:hover:bg-primary/30;
}

.upload-dropzone {
  @apply flex h-72 w-full cursor-pointer flex-col items-center justify-center rounded-xl border-2 border-dashed border-border/60 dark:border-border/40 bg-secondary/30 dark:bg-secondary/20 p-10 transition-all duration-300 hover:border-primary/50 dark:hover:border-primary/40;
}

@layer utilities {
  .glass {
    @apply bg-background/80 backdrop-blur-sm border border-border/20 dark:bg-background/40 dark:border-border/10;
  }
  
  .hover-scale {
    @apply transition-transform duration-200 hover:scale-[1.02];
  }
  
  .stockfill-shadow {
    @apply shadow-md dark:shadow-lg dark:shadow-black/20;
  }
  
  .stockfill-gradient {
    @apply bg-gradient-to-r from-primary/90 to-primary bg-clip-text text-transparent;
  }
  
  .stockfill-card {
    @apply rounded-lg border border-border/40 bg-card/95 shadow-md transition-all duration-200 hover:shadow-lg dark:bg-card/90 dark:backdrop-blur-sm;
  }
}

@tailwind base;
@tailwind components;
@tailwind utilities;
 
@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --primary: 142.1 76.2% 36.3%;
    --primary-foreground: 355.7 100% 97.3%;
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 142.1 76.2% 36.3%;
    --radius: 0.5rem;
  }
 
  .dark {
    /* Enhanced dark theme colors inspired by Stockfill */
    --background: 240 10% 4%; /* Darker background */
    --foreground: 0 0% 98%;
    --card: 240 10% 6%; /* Slightly lighter than background for cards */
    --card-foreground: 0 0% 98%;
    --popover: 240 10% 6%;
    --popover-foreground: 0 0% 98%;
    --primary: 142.1 70.6% 45.3%; /* Vibrant green */
    --primary-foreground: 144.9 80.4% 10%;
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 142.4 71.8% 29.2%;
  }
}
 
@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Additional custom styles for Stockfill-like UI */
.image-card {
  @apply relative overflow-hidden rounded-md border border-border bg-card transition-all hover:shadow-md;
}

.image-card-selected {
  @apply border-primary;
}

.sidebar-nav-item {
  @apply flex items-center gap-2 rounded-md px-3 py-2 text-sm font-medium transition-colors hover:bg-secondary;
}

.sidebar-nav-item-active {
  @apply bg-secondary text-foreground;
}

.metadata-panel {
  @apply space-y-4 rounded-md bg-card p-4 shadow-sm;
}

.upload-dropzone {
  @apply flex h-64 w-full cursor-pointer flex-col items-center justify-center rounded-md border-2 border-dashed border-border bg-secondary/50 p-6 transition-colors hover:border-primary/50 hover:bg-secondary;
}