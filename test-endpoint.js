// Test the manual completion endpoint directly
const testEndpoint = async () => {
  try {
    console.log('Testing manual completion endpoint...');

    const response = await fetch('http://localhost:5174/api/payments/test/complete-payment', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        payment_id: 'pay_CZFuJMFg2SfdNQTSoHLdz',
        subscription_id: 'sub_4zYq64uxvW5iwwkXBBnDx',
        user_id: 'ba40622d-eaae-4437-838a-28d3910360b4',
        profile_id: 'ba40622d-eaae-4437-838a-28d3910360b4',
        amount: 1000
      })
    });

    console.log('Response status:', response.status);
    console.log('Response URL:', response.url);

    const text = await response.text();
    console.log('Response text:', text);

  } catch (error) {
    console.error('Request failed:', error);
  }
};

testEndpoint();
