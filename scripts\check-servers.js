#!/usr/bin/env node

/**
 * Quick script to check if both frontend and backend servers are running
 */

async function checkServers() {
  console.log('🔍 Checking server status...\n');

  // Check frontend server
  try {
    const frontendResponse = await fetch('http://localhost:5174');
    if (frontendResponse.ok) {
      console.log('✅ Frontend server: Running on http://localhost:5174');
    } else {
      console.log('❌ Frontend server: Not responding properly');
    }
  } catch (error) {
    console.log('❌ Frontend server: Not running on http://localhost:5174');
    console.log('   💡 Start with: npm run dev:frontend');
  }

  // Check backend server
  try {
    const backendResponse = await fetch('http://localhost:3002/api/health');
    if (backendResponse.ok) {
      const data = await backendResponse.json();
      console.log('✅ Backend server: Running on http://localhost:3002');
      console.log('   📊 Health check:', data);
    } else {
      console.log('❌ Backend server: Not responding properly');
    }
  } catch (error) {
    console.log('❌ Backend server: Not running on http://localhost:3002');
    console.log('   💡 Start with: npm run dev:backend');
  }

  // Check payment API specifically
  try {
    const paymentHealthResponse = await fetch('http://localhost:3002/api/payments/health');
    if (paymentHealthResponse.ok) {
      const data = await paymentHealthResponse.json();
      console.log('✅ Payment API: Working');
      console.log('   📊 Payment health:', data);
    } else {
      console.log('❌ Payment API: Not responding properly');
    }
  } catch (error) {
    console.log('❌ Payment API: Not accessible');
  }

  console.log('\n🎯 Summary:');
  console.log('If both servers are running, your payment system should work!');
  console.log('If not, start the missing server(s) and try again.');
}

// Check if fetch is available (Node.js 18+)
if (typeof fetch === 'undefined') {
  console.log('❌ This script requires Node.js 18+ or you need to install node-fetch');
  console.log('💡 Try: npm install node-fetch');
  process.exit(1);
}

checkServers();
