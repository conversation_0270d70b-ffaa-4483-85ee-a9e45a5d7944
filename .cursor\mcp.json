{"mcpServers": {"dodopayments_api": {"command": "npx", "args": ["-y", "dodopayments-mcp", "--client=cursor"], "env": {"DODO_PAYMENTS_API_KEY": "Y5h-0edhftQ-_aWv.UDaLs-NfZ0DRsjzWCVZztJH_9xCF9UL7dHbe34fZZyDQd6Ij"}}, "supabase-project1": {"command": "npx", "args": ["@supabase/mcp-server-supabase@latest", "--access-token", "********************************************", "--project-id", "alywdwwqrtddplqsbksd"], "env": {"SUPABASE_ACCESS_TOKEN": "********************************************", "SUPABASE_PROJECT_ID": "alywdwwqrtddplqsbksd"}}}}