import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/hooks/use-toast';
import { useProfileStore } from '@/stores/profileStore';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Copy, RefreshCw, CreditCard, User, Database } from 'lucide-react';

const PaymentDebug = () => {
  const { profile, refreshProfile } = useProfileStore();
  const { toast } = useToast();
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [webhookUrl, setWebhookUrl] = useState('');
  const [testResult, setTestResult] = useState('');
  const [apiTestResult, setApiTestResult] = useState('');

  const handleRefreshProfile = async () => {
    setIsRefreshing(true);
    try {
      await refreshProfile();
      toast({
        title: 'Profile Refreshed',
        description: 'User profile data has been updated.',
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to refresh profile.',
        variant: 'destructive',
      });
    } finally {
      setIsRefreshing(false);
    }
  };

  const copyToClipboard = (text: string, label: string) => {
    navigator.clipboard.writeText(text);
    toast({
      title: 'Copied!',
      description: `${label} copied to clipboard.`,
    });
  };

  const createWebhookSignature = (payload: string, secret: string) => {
    const crypto = window.crypto;
    const timestamp = Math.floor(Date.now() / 1000).toString();
    const webhookId = 'wh_' + Array.from(crypto.getRandomValues(new Uint8Array(16)), b => b.toString(16).padStart(2, '0')).join('');

    // For browser compatibility, we'll skip the actual HMAC signature
    // The server will need to handle test webhooks without signature verification
    const signature = 'test_signature_' + Date.now();

    return {
      signature,
      timestamp,
      webhookId
    };
  };

  const testWebhook = async () => {
    if (!profile?.id) {
      toast({
        title: 'Error',
        description: 'No profile ID available. Please refresh the page and try again.',
        variant: 'destructive',
      });
      return;
    }

    try {
      setTestResult('Testing webhook...');

      // Create a test webhook payload
      const testPayload = {
        type: 'payment.succeeded',
        data: {
          id: 'pay_test_' + Date.now(),
          customerId: profile.customer_id || 'cus_test_' + Date.now(),
          amount: 1000, // $10.00
          currency: 'USD',
          status: 'succeeded',
          metadata: {
            profile_id: profile.id,
            user_id: profile.id,
            user_email: profile.email,
            credits: 100,
            test: true,
            payment_type: 'debug_test',
            timestamp: new Date().toISOString()
          }
        }
      };

      // Use the backend URL for webhook testing
      const backendPort = import.meta.env.VITE_BACKEND_PORT || '3001';
      const url = webhookUrl || `http://localhost:${backendPort}/api/webhooks/dodo-payments`;

      const payload = JSON.stringify(testPayload);
      const { signature, timestamp, webhookId } = createWebhookSignature(payload, 'test_secret');

      setTestResult(`Testing webhook at: ${url}\nPayload: ${JSON.stringify(testPayload, null, 2)}\n\nSending request...`);

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'webhook-signature': signature,
          'webhook-id': webhookId,
          'webhook-timestamp': timestamp,
        },
        body: payload
      });

      const result = await response.text();
      setTestResult(`Status: ${response.status}\nResponse: ${result}\n\nURL: ${url}`);

      if (response.ok) {
        toast({
          title: 'Webhook Test Successful',
          description: 'Check your credits to see if they were added.',
        });
        // Refresh profile to see updated credits
        await refreshProfile();
      } else {
        toast({
          title: 'Webhook Test Failed',
          description: `Status: ${response.status}. Check the test result for details.`,
          variant: 'destructive',
        });
      }
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : 'Unknown error';
      setTestResult(`Error: ${errorMsg}\n\nThis might mean:\n1. Backend server is not running\n2. Network connectivity issue\n3. CORS configuration problem`);
      toast({
        title: 'Test Failed',
        description: errorMsg,
        variant: 'destructive',
      });
    }
  };

  const testPaymentAPI = async () => {
    if (!profile?.id) {
      toast({
        title: 'Error',
        description: 'No profile ID available. Please refresh the page and try again.',
        variant: 'destructive',
      });
      return;
    }

    try {
      setApiTestResult('Testing payment API...');

      // Test the same endpoint that's failing in DodoPaymentTest
      const testData = {
        user_id: profile.id,
        profile_id: profile.id,
        user_email: profile.email || '<EMAIL>'
      };

      setApiTestResult(`Testing API at: /api/payments/test/checkout\nPayload: ${JSON.stringify(testData, null, 2)}\n\nSending request...`);

      const response = await fetch('/api/payments/test/checkout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(testData)
      });

      const result = await response.text();
      setApiTestResult(`Status: ${response.status}\nResponse: ${result}\n\nEndpoint: /api/payments/test/checkout`);

      if (response.ok) {
        toast({
          title: 'API Test Successful',
          description: 'Payment API endpoint is working correctly.',
        });
      } else {
        toast({
          title: 'API Test Failed',
          description: `Status: ${response.status}. This is the same error you're seeing in the payment test.`,
          variant: 'destructive',
        });
      }
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : 'Unknown error';
      setApiTestResult(`Error: ${errorMsg}\n\nThis means:\n1. Backend server is not running (most likely)\n2. API routes are not properly configured\n3. Network connectivity issue`);
      toast({
        title: 'API Test Failed',
        description: errorMsg,
        variant: 'destructive',
      });
    }
  };

  return (
    <div className="min-h-screen bg-background p-4">
      <div className="max-w-4xl mx-auto space-y-6">
        <div className="text-center">
          <h1 className="text-3xl font-bold">Payment System Debug</h1>
          <p className="text-muted-foreground mt-2">
            Debug and test the payment system integration
          </p>
        </div>

        {/* Profile Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              Profile Information
            </CardTitle>
            <CardDescription>
              Current user profile and payment details
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label>Profile ID</Label>
                <div className="flex items-center gap-2 mt-1">
                  <Input value={profile?.id || 'Not available'} readOnly />
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => copyToClipboard(profile?.id || '', 'Profile ID')}
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              
              <div>
                <Label>Customer ID</Label>
                <div className="flex items-center gap-2 mt-1">
                  <Input value={profile?.customer_id || 'Not set'} readOnly />
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => copyToClipboard(profile?.customer_id || '', 'Customer ID')}
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <Label>Credits</Label>
                <div className="mt-1">
                  <Badge variant="secondary" className="text-lg px-3 py-1">
                    {profile?.credits || 0}
                  </Badge>
                </div>
              </div>
              
              <div>
                <Label>Permanent Credits</Label>
                <div className="mt-1">
                  <Badge variant="outline" className="text-lg px-3 py-1">
                    {profile?.permanent_credits || 0}
                  </Badge>
                </div>
              </div>
              
              <div>
                <Label>Subscription Plan</Label>
                <div className="mt-1">
                  <Badge variant="default" className="text-lg px-3 py-1">
                    {profile?.subscription_plan || 'free'}
                  </Badge>
                </div>
              </div>
            </div>

            <Button onClick={handleRefreshProfile} disabled={isRefreshing}>
              <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
              Refresh Profile
            </Button>
          </CardContent>
        </Card>

        {/* API Testing */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CreditCard className="h-5 w-5" />
              Payment API Testing
            </CardTitle>
            <CardDescription>
              Test the payment API endpoint that's failing in the payment test page
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Button onClick={testPaymentAPI} className="w-full" variant="outline">
              <Database className="h-4 w-4 mr-2" />
              Test Payment API Endpoint
            </Button>

            {apiTestResult && (
              <div>
                <Label>API Test Result</Label>
                <Textarea
                  value={apiTestResult}
                  readOnly
                  className="mt-1 font-mono text-sm"
                  rows={8}
                />
              </div>
            )}
          </CardContent>
        </Card>

        {/* Webhook Testing */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Database className="h-5 w-5" />
              Webhook Testing
            </CardTitle>
            <CardDescription>
              Test the webhook endpoint to simulate payment events
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="webhook-url">Webhook URL (optional)</Label>
              <Input
                id="webhook-url"
                placeholder={`http://localhost:${import.meta.env.VITE_BACKEND_PORT || '3001'}/api/webhooks/dodo-payments`}
                value={webhookUrl}
                onChange={(e) => setWebhookUrl(e.target.value)}
              />
              <p className="text-sm text-muted-foreground mt-1">
                Leave empty to use default webhook endpoint
              </p>
            </div>

            <Button onClick={testWebhook} className="w-full">
              <CreditCard className="h-4 w-4 mr-2" />
              Test Payment Webhook (Add 100 Credits)
            </Button>

            {testResult && (
              <div>
                <Label>Test Result</Label>
                <Textarea
                  value={testResult}
                  readOnly
                  className="mt-1 font-mono text-sm"
                  rows={6}
                />
              </div>
            )}
          </CardContent>
        </Card>

        {/* Environment Info */}
        <Card>
          <CardHeader>
            <CardTitle>Environment Information</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span>Environment:</span>
                <Badge variant="outline">{import.meta.env.MODE}</Badge>
              </div>
              <div className="flex justify-between">
                <span>Dodo Payments Mode:</span>
                <Badge variant="outline">{import.meta.env.VITE_DODO_PAYMENTS_MODE}</Badge>
              </div>
              <div className="flex justify-between">
                <span>API URL:</span>
                <span className="font-mono text-xs">{import.meta.env.VITE_DODO_PAYMENTS_API_URL}</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default PaymentDebug;
