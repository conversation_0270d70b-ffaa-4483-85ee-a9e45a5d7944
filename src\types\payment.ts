/**
 * Payment types for Dodo Payments integration
 */

export enum PaymentStatus {
  PENDING = 'pending',
  SUCCESS = 'success',
  FAILED = 'failed',
  CANCELED = 'canceled'
}

export enum SubscriptionStatus {
  ACTIVE = 'active',
  ON_HOLD = 'on_hold',
  FAILED = 'failed',
  CANCELED = 'canceled'
}

export interface BillingAddress {
  street: string;
  city: string;
  state: string;
  country: string;
  zipcode: string;
}

export interface PaymentCustomer {
  customer_id?: string;
  email?: string;
  name?: string;
}

export interface ProductItem {
  product_id: string;
  amount: number; // Amount in cents
  quantity?: number;
}

export interface CreatePaymentRequest {
  billing_currency?: string;
  allowed_payment_method_types?: string[];
  product_cart: ProductItem[];
  return_url: string;
  customer?: PaymentCustomer;
  billing?: BillingAddress;
  metadata?: Record<string, any>;
}

export interface PaymentResponse {
  id: string;
  url: string;
  checkout_url?: string;
  status: string;
  expires_at?: string;
}

export interface CreateSubscriptionRequest {
  customer: PaymentCustomer;
  product_id: string;
  billing: BillingAddress;
  payment_link?: boolean;
  subscription_id?: string;
  quantity?: number;
  metadata?: Record<string, any>;
}

export interface SubscriptionResponse {
  subscription_id: string;
  customer_id: string;
  status: string;
  product_id: string;
  payment_url?: string;
}

export interface WebhookEvent {
  business_id: string;
  timestamp: string;
  type: string;
  data: Record<string, any>;
}

export interface PaymentRecord {
  _id?: string;
  payment_id: string;
  user_id?: string;
  customer_id?: string;
  amount: number;
  currency: string;
  status: PaymentStatus;
  product_id?: string;
  metadata?: Record<string, any>;
  created_at: Date;
  updated_at: Date;
}

export interface SubscriptionRecord {
  _id?: string;
  subscription_id: string;
  user_id?: string;
  customer_id: string;
  product_id: string;
  status: SubscriptionStatus;
  current_period_start?: Date;
  current_period_end?: Date;
  cancel_at_period_end?: boolean;
  metadata?: Record<string, any>;
  created_at: Date;
  updated_at: Date;
}