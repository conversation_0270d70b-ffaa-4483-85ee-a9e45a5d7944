#!/usr/bin/env node

/**
 * Simple script to test if the API endpoints are working
 */

async function testAPI() {
  console.log('🧪 Testing API endpoints...\n');

  // Test health endpoint
  try {
    console.log('1. Testing health endpoint...');
    const healthResponse = await fetch('http://localhost:3000/api/health');
    if (healthResponse.ok) {
      const healthData = await healthResponse.json();
      console.log('✅ Health endpoint working:', healthData);
    } else {
      console.log('❌ Health endpoint failed:', healthResponse.status);
    }
  } catch (error) {
    console.log('❌ Health endpoint error:', error.message);
    console.log('💡 Make sure the backend server is running: npm run dev:backend');
    return;
  }

  // Test payments health endpoint
  try {
    console.log('\n2. Testing payments health endpoint...');
    const paymentsHealthResponse = await fetch('http://localhost:3000/api/payments/health');
    if (paymentsHealthResponse.ok) {
      const paymentsHealthData = await paymentsHealthResponse.json();
      console.log('✅ Payments health endpoint working:', paymentsHealthData);
    } else {
      console.log('❌ Payments health endpoint failed:', paymentsHealthResponse.status);
    }
  } catch (error) {
    console.log('❌ Payments health endpoint error:', error.message);
  }

  // Test the problematic test checkout endpoint
  try {
    console.log('\n3. Testing test checkout endpoint...');
    const testData = {
      user_id: 'test-user-id',
      profile_id: 'test-profile-id',
      user_email: '<EMAIL>'
    };

    const testResponse = await fetch('http://localhost:3000/api/payments/test/checkout', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(testData)
    });

    if (testResponse.ok) {
      const testData = await testResponse.json();
      console.log('✅ Test checkout endpoint working:', testData);
    } else {
      const errorText = await testResponse.text();
      console.log('❌ Test checkout endpoint failed:', testResponse.status, errorText);
    }
  } catch (error) {
    console.log('❌ Test checkout endpoint error:', error.message);
  }

  console.log('\n🎯 API test completed!');
}

// Check if fetch is available (Node.js 18+)
if (typeof fetch === 'undefined') {
  console.log('❌ This script requires Node.js 18+ or you need to install node-fetch');
  console.log('💡 Try: npm install node-fetch');
  process.exit(1);
}

testAPI();
